name: life_habit_app
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.1.1+47

environment:
  sdk: '>=3.3.0 <4.0.0'

ios:
  bundleIdentifier: com.dxwvv.LifeHabit

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  cupertino_icons: ^1.0.8
  get: ^4.7.2
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  flutter_screenutil: ^5.9.3
  dio: ^5.8.0+1
  fluttertoast: ^8.2.12
  qiniu_flutter_sdk: ^0.7.3
  get_storage: ^2.1.1
  intl: ^0.20.2
  timezone: ^0.10.0
  getwidget: ^7.0.0
  image_picker: ^1.1.2
  cached_network_image: ^3.4.1
  scrollable_clean_calendar: ^1.5.0
  flutter_cache_manager: ^3.4.1
  photo_view: ^0.15.0
  flutter_advanced_switch: ^3.1.0
  dashed_circular_progress_bar: ^0.0.6
  fl_chart: ^1.0.0
  webview_flutter: ^4.11.0
  url_launcher: ^6.3.1
  flutter_colorpicker: ^1.1.0
  table_calendar: ^3.1.3
  flutter_slidable: ^4.0.0
  share_plus: ^11.0.0
  path_provider: ^2.1.4

  # UI and animations
  flutter_svg: ^2.1.0
  shared_preferences: ^2.5.3
  flutter_local_notifications: ^19.2.1
  shimmer: ^3.0.0
  lottie: ^3.3.1
  device_info_plus: ^11.4.0
  package_info_plus: ^8.3.0

  # 推送通知相关依赖
  firebase_core: ^3.13.1
  firebase_messaging: ^15.2.6
  permission_handler: ^12.0.0+1
  app_badge_plus: ^1.2.3

  # 极光推送（国内Android用户）
  jpush_flutter: ^3.2.8

  # Firebase 认证相关依赖
  firebase_auth: ^5.5.4
  google_sign_in: ^6.3.0
  sign_in_with_apple: ^7.0.1
  crypto: ^3.0.6

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^6.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true
  fonts:
    - family: custom_font_v1
      fonts:
        - asset: assets/fonts/iconfont.ttf
  assets:
    - assets/images/
    - assets/icons/

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages

# 暂时注释掉可能冲突的URL Scheme配置
# tobias:
#   url_scheme: com.example.lifehabit
#   ios:
#     ignore_security: true
#     universal_link: https://testdomain.com
