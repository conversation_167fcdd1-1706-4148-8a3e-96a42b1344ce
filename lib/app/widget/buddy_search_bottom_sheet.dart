import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import '../models/buddy_model.dart';
import '../data/dao/buddy_dao.dart';

import '../theme/app_colors.dart';
import '../utils/customFonts.dart';
import '../utils/toast.dart';
import '../utils/hi_constants.dart';
import '../core/net_core/hi_error.dart';

class BuddySearchBottomSheet extends StatefulWidget {
  final Function(BuddyUser buddyUser)? onBuddySelected;

  const BuddySearchBottomSheet({
    super.key,
    this.onBuddySelected,
  });

  @override
  State<BuddySearchBottomSheet> createState() => _BuddySearchBottomSheetState();
}

class _BuddySearchBottomSheetState extends State<BuddySearchBottomSheet>
    with TickerProviderStateMixin {
  final TextEditingController _searchController = TextEditingController();

  final RxList<BuddyUser> _searchResults = <BuddyUser>[].obs;
  final RxBool _isLoading = false.obs;
  final RxBool _hasSearched = false.obs;
  final RxString _errorMessage = ''.obs;
  final RxList<String> _addingBuddies = <String>[].obs; // 正在添加的搭子ID列表
  final RxString _searchText = ''.obs; // 用于监听搜索框文本变化

  final RxBool _canAddBuddy = true.obs; // 是否还能添加搭子

  // 防抖定时器
  Timer? _debounceTimer;

  // 动画控制器
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();

    // 初始化动画控制器
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    // 创建淡入淡出动画
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    // 启动入场动画
    _animationController.forward();
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    _animationController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  /// 带动画的关闭方法
  Future<void> _closeWithAnimation() async {
    // 执行淡出动画
    await _animationController.reverse();
    // 动画完成后关闭
    if (mounted) {
      Navigator.of(context).pop();
    }
  }



  /// 搜索搭子（带防抖，UID固定10位）
  void _searchBuddies(String query) {
    // 取消之前的定时器
    _debounceTimer?.cancel();

    if (query.trim().isEmpty) {
      _searchResults.clear();
      _hasSearched.value = false;
      _errorMessage.value = '';
      return;
    }

    final trimmedQuery = query.trim().toUpperCase();

    // UID固定10位，只有输入完整才搜索
    if (trimmedQuery.length == 10) {
      // 设置防抖定时器，500ms后执行搜索
      _debounceTimer = Timer(const Duration(milliseconds: 500), () {
        _performSearch(trimmedQuery);
      });
    } else {
      // 长度不够10位，清空结果但不显示错误
      _searchResults.clear();
      _hasSearched.value = false;
      _errorMessage.value = '';
    }
  }

  /// 执行实际的搜索请求
  Future<void> _performSearch(String normalizedQuery) async {
    _isLoading.value = true;
    _hasSearched.value = true;
    _errorMessage.value = '';

    try {
      final result = await BuddyDao.searchBuddiesByUid(uid: normalizedQuery);
      _searchResults.value = result.users;

      // 更新搭子状态信息
      _canAddBuddy.value = result.canAddBuddy;

      if (result.users.isEmpty) {
        _errorMessage.value = '未找到相关用户'.tr;
      }
    } catch (e) {
      _errorMessage.value = '搜索失败，请重试'.tr;
      _searchResults.clear();
    } finally {
      _isLoading.value = false;
    }
  }

  /// 显示消息输入对话框并添加搭子
  Future<void> _addBuddy(BuddyUser user) async {
    if (_addingBuddies.contains(user.uid)) return;

    // 显示消息输入对话框
    final message = await _showMessageInputDialog(user);
    if (message == null) return; // 用户取消了

    _addingBuddies.add(user.uid);

    try {
      await BuddyDao.sendBuddyInvitation(
        toUserUID: user.uid,
        message: message,
      );

      // 显示成功提示
      showSuccessToast('邀请已发送给 ${user.nickname}'.tr);

      // 调用回调
      widget.onBuddySelected?.call(user);

      // 使用动画关闭
      await _closeWithAnimation();
    } catch (e) {
      // 提取错误消息
      String errorMessage = '发送邀请失败，请重试'.tr;
      String errorType = HiConstants.errorTypeOther;

      // 根据错误类型提取具体消息和错误类型
      if (e is ParamsError) {
        errorMessage = e.message;
        errorType = HiConstants.errorTypeWarning; // 参数错误通常是警告类型
      } else if (e is HiNetError) {
        errorMessage = e.message;
        errorType = HiConstants.errorTypeNetwork; // 网络错误
      }

      showErrorMessage(errorType, errorMessage);
    } finally {
      _addingBuddies.remove(user.uid);
    }
  }

  /// 显示消息输入对话框
  Future<String?> _showMessageInputDialog(BuddyUser user) async {
    final TextEditingController messageController = TextEditingController();
    final RxString currentMessage = ''.obs;

    // 预设消息模板 - 支持国际化
    final List<String> messageTemplates = [
      '希望我们能一起养成好习惯！'.tr,
      '一起坚持，互相监督吧～'.tr,
      '让我们成为习惯搭子，共同进步！'.tr,
      '想和你一起培养好习惯，加油！'.tr,
    ];

    // 默认使用第一个模板
    messageController.text = messageTemplates[0];
    currentMessage.value = messageTemplates[0];

    return await showDialog<String>(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext dialogContext) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Container(
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 标题
                Row(
                  children: [
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: Colors.grey.shade100,
                        border: Border.all(
                          color: AppColors.primary.withValues(alpha: 0.1),
                          width: 2,
                        ),
                        image: user.avatarUrl != null && user.avatarUrl!.isNotEmpty
                            ? DecorationImage(
                                image: NetworkImage(user.avatarUrl!),
                                fit: BoxFit.cover,
                              )
                            : null,
                      ),
                      child: user.avatarUrl == null || user.avatarUrl!.isEmpty
                          ? Icon(
                              Icons.person_rounded,
                              color: AppColors.primary.withValues(alpha: 0.6),
                              size: 20,
                            )
                          : null,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '${'邀请'.tr} ${user.nickname}',
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.w600,
                              color: Color(0xFF333333),
                            ),
                          ),
                          Text(
                            '成为你的习惯搭子'.tr,
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey.shade600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 20),

                // 消息输入标题
                Text(
                  '邀请消息'.tr,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF333333),
                  ),
                ),

                const SizedBox(height: 12),

                // 消息输入框
                Container(
                  decoration: BoxDecoration(
                    color: const Color(0xFFF8F8F8),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: const Color(0xFFE8E8E8)),
                  ),
                  child: TextField(
                    controller: messageController,
                    maxLines: 3,
                    maxLength: 100,
                    onChanged: (value) => currentMessage.value = value,
                    decoration: InputDecoration(
                      hintText: '写点什么来介绍自己吧...'.tr,
                      hintStyle: TextStyle(
                        color: Colors.grey.shade500,
                        fontSize: 14,
                      ),
                      border: InputBorder.none,
                      contentPadding: const EdgeInsets.all(16),
                      counterStyle: TextStyle(
                        color: Colors.grey.shade500,
                        fontSize: 12,
                      ),
                    ),
                    style: const TextStyle(
                      fontSize: 14,
                      color: Color(0xFF333333),
                    ),
                  ),
                ),

                const SizedBox(height: 16),

                // 快速模板
                Text(
                  '快速选择'.tr,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey.shade700,
                  ),
                ),

                const SizedBox(height: 8),

                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: messageTemplates.map((template) {
                    return Obx(() => GestureDetector(
                      onTap: () {
                        messageController.text = template;
                        currentMessage.value = template;
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: currentMessage.value == template
                              ? AppColors.primary.withValues(alpha: 0.1)
                              : Colors.grey.shade100,
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: currentMessage.value == template
                                ? AppColors.primary.withValues(alpha: 0.3)
                                : Colors.grey.shade300,
                          ),
                        ),
                        child: Text(
                          template,
                          style: TextStyle(
                            fontSize: 12,
                            color: currentMessage.value == template
                                ? AppColors.primary
                                : Colors.grey.shade700,
                            fontWeight: currentMessage.value == template
                                ? FontWeight.w500
                                : FontWeight.normal,
                          ),
                        ),
                      ),
                    ));
                  }).toList(),
                ),

                const SizedBox(height: 24),

                // 按钮
                Row(
                  children: [
                    Expanded(
                      child: TextButton(
                        onPressed: () => Navigator.of(dialogContext).pop(null),
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                            side: BorderSide(color: Colors.grey.shade300),
                          ),
                        ),
                        child: Text(
                          '取消'.tr,
                          style: TextStyle(
                            color: Colors.grey.shade700,
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () {
                          final message = messageController.text.trim();
                          // 允许发送空消息，使用默认消息
                          final finalMessage = message.isEmpty ? messageTemplates[0] : message;
                          Navigator.of(dialogContext).pop(finalMessage);
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primary,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          elevation: 0,
                        ),
                        child: Text(
                          '发送邀请'.tr,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Container(
        width: double.infinity,
        height: MediaQuery.of(context).size.height * 0.4,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 头部
          _buildHeader(),

          // 搜索栏
          _buildSearchBar(),

          // 可滚动内容区域
          Flexible(
            child: SingleChildScrollView(
              physics: const ClampingScrollPhysics(),
              child: _buildContent(),
            ),
          ),
        ],
        ),
      ),
    );
  }

  /// 构建头部
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.fromLTRB(20, 24, 20, 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
        border: Border(
          bottom: BorderSide(color: const Color(0xFFF5F5F5), width: 1),
        ),
      ),
      child: Row(
        children: [
          // 关闭按钮
          GestureDetector(
            onTap: () => Get.back(),
            child: Container(
              width: 36,
              height: 36,
              decoration: BoxDecoration(
                color: const Color(0xFFF8F8F8),
                shape: BoxShape.circle,
                border: Border.all(color: const Color(0xFFE8E8E8), width: 1),
              ),
              child: Icon(
                Icons.close_rounded,
                size: 20,
                color: Colors.grey.shade600,
              ),
            ),
          ),
          const SizedBox(width: 16),
          // 标题区域
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '寻找习惯搭子'.tr,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF333333),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '找到志同道合的伙伴，一起坚持好习惯'.tr,
                  style: const TextStyle(
                    fontSize: 13,
                    color: Color(0xFF666666),
                    height: 1.3,
                  ),
                ),


              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建搜索栏
  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
      child: Container(
        height: 48,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(24),
          border: Border.all(color: const Color(0xFFE8E8E8), width: 1.5),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.04),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: TextField(
          controller: _searchController,
          inputFormatters: [
            // 自动转换为大写
            UpperCaseTextFormatter(),
            // 限制长度为10位
            LengthLimitingTextInputFormatter(10),
          ],
          onChanged: (value) {
            _searchText.value = value;
            // 防抖搜索
            Future.delayed(const Duration(milliseconds: 500), () {
              if (_searchController.text == value) {
                _searchBuddies(value);
              }
            });
          },
          decoration: InputDecoration(
            hintText: '输入用户UID'.tr,
            hintStyle: const TextStyle(
              color: Color(0xFF999999),
              fontSize: 15,
            ),
            prefixIcon: Container(
              padding: const EdgeInsets.all(12),
              child: Icon(
                Icons.search_rounded,
                color: AppColors.primary.withValues(alpha: 0.6),
                size: 22,
              ),
            ),
            suffixIcon: Obx(() => _searchText.value.isNotEmpty
                ? GestureDetector(
                    onTap: () {
                      _searchController.clear();
                      _searchText.value = '';
                      _searchBuddies('');
                    },
                    child: Container(
                      padding: const EdgeInsets.all(12),
                      child: Icon(
                        Icons.clear_rounded,
                        color: Colors.grey.shade400,
                        size: 20,
                      ),
                    ),
                  )
                : const SizedBox.shrink()),
            border: InputBorder.none,
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 20,
              vertical: 14,
            ),
          ),
          style: const TextStyle(
            fontSize: 15,
            color: Color(0xFF333333),
          ),
        ),
      ),
    );
  }

  /// 构建内容区域
  Widget _buildContent() {
    return Obx(() {
      if (_isLoading.value) {
        return _buildLoadingState();
      }

      if (_errorMessage.value.isNotEmpty) {
        return _buildErrorState();
      }

      if (_searchResults.isEmpty) {
        return _buildEmptyState();
      }

      return _buildUserList();
    });
  }

  /// 构建加载状态
  Widget _buildLoadingState() {
    return Container(
      height: 200, // 给加载状态一个固定高度，确保居中显示
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 32,
              height: 32,
              child: CircularProgressIndicator(
                color: AppColors.primary,
                strokeWidth: 2.5,
              ),
            ),
            const SizedBox(height: 20),
            Text(
              '搜索中...',
              style: TextStyle(
                color: Color(0xFF666666),
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建错误状态
  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 48,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            _errorMessage.value,
            style: const TextStyle(
              color: Color(0xFF666666),
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 16),
          TextButton(
            onPressed: () {
              if (_hasSearched.value && _searchController.text.isNotEmpty) {
                _searchBuddies(_searchController.text);
              } else {
                _errorMessage.value = '';
              }
            },
            child: Text(
              '重试'.tr,
              style: const TextStyle(
                color: AppColors.primary,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建空状态
  Widget _buildEmptyState() {
    if (_hasSearched.value) {
      // 搜索无结果状态
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: AppColors.primary.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.search_off_rounded,
                size: 60,
                color: AppColors.primary.withValues(alpha: 0.6),
              ),
            ),
            const SizedBox(height: 24),
            Text(
              '未找到相关用户'.tr,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Color(0xFF333333),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '试试其他关键词或邀请朋友加入'.tr,
              style: const TextStyle(
                fontSize: 14,
                color: Color(0xFF666666),
              ),
            ),
          ],
        ),
      );
    }

    // 默认占位符状态
    return _buildPlaceholderState();
  }

  /// 构建占位符状态
  Widget _buildPlaceholderState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          // 主要插图
          Container(
            width: 140,
            height: 140,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  AppColors.primary.withValues(alpha: 0.1),
                  AppColors.primary.withValues(alpha: 0.05),
                ],
              ),
              shape: BoxShape.circle,
            ),
            child: Stack(
              alignment: Alignment.center,
              children: [
                // 背景圆圈
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.15),
                    shape: BoxShape.circle,
                  ),
                ),
                // 主图标
                Icon(
                  Icons.people_alt_rounded,
                  size: 40,
                  color: AppColors.primary,
                ),
                // 装饰性小图标
                Positioned(
                  top: 15,
                  right: 15,
                  child: Container(
                    width: 20,
                    height: 20,
                    decoration: BoxDecoration(
                      color: Colors.orange.withValues(alpha: 0.2),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.star_rounded,
                      size: 12,
                      color: Colors.orange,
                    ),
                  ),
                ),
                Positioned(
                  bottom: 20,
                  left: 10,
                  child: Container(
                    width: 18,
                    height: 18,
                    decoration: BoxDecoration(
                      color: Colors.green.withValues(alpha: 0.2),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.favorite_rounded,
                      size: 10,
                      color: Colors.green,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建用户列表
  Widget _buildUserList() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 列表标题
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            children: [
              Text(
                '搜索结果'.tr,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF333333),
                ),
              ),
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Text(
                  '${_searchResults.length}',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: AppColors.primary,
                  ),
                ),
              ),
            ],
          ),
        ),
        // 用户列表
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          padding: const EdgeInsets.symmetric(horizontal: 16),
          itemCount: _searchResults.length,
          itemBuilder: (context, index) {
            final user = _searchResults[index];
            return _buildUserCard(user);
          },
        ),
      ],
    );
  }

  /// 构建用户卡片
  Widget _buildUserCard(BuddyUser user) {
    return Obx(() {
      final isAdding = _addingBuddies.contains(user.uid);

      return Container(
        margin: const EdgeInsets.only(bottom: 16),
        padding: const EdgeInsets.all(18),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: const Color(0xFFE8E8E8), width: 1),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.06),
              blurRadius: 12,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Row(
          children: [
            // 头像
            Container(
              width: 56,
              height: 56,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.grey.shade100,
                border: Border.all(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  width: 2,
                ),
                image: user.avatarUrl != null && user.avatarUrl!.isNotEmpty
                    ? DecorationImage(
                        image: NetworkImage(user.avatarUrl!),
                        fit: BoxFit.cover,
                      )
                    : null,
              ),
              child: user.avatarUrl == null || user.avatarUrl!.isEmpty
                  ? Icon(
                      Icons.person_rounded,
                      color: AppColors.primary.withValues(alpha: 0.6),
                      size: 28,
                    )
                  : null,
            ),
            const SizedBox(width: 12),
            // 用户信息
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    user.nickname,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF333333),
                    ),
                  ),
                  if (user.description != null) ...[
                    const SizedBox(height: 4),
                    Text(
                      user.description!,
                      style: const TextStyle(
                        fontSize: 13,
                        color: Color(0xFF666666),
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                  const SizedBox(height: 6),
                  Row(
                    children: [
                      Icon(
                        CustomFonts.mutilStar,
                        size: 12,
                        color: Colors.grey.shade500,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${user.habitCount ?? 0} 个习惯',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            // 添加按钮和状态
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                // 按钮
                _buildUserActionButton(user, isAdding),

                // 状态说明文本
                if (user.status != BuddyStatus.none)
                  Padding(
                    padding: const EdgeInsets.only(top: 4),
                    child: Text(
                      _getStatusDescription(user.status),
                      style: TextStyle(
                        fontSize: 11,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ),
              ],
            ),
          ],
        ),
      );
    });
  }

  /// 构建用户操作按钮
  Widget _buildUserActionButton(BuddyUser user, bool isAdding) {
      // 确定按钮状态和文案
      String buttonText;
      bool isClickable;
      Color? backgroundColor;
      Color textColor;

      if (isAdding) {
        buttonText = '添加中';
        isClickable = false;
        backgroundColor = Colors.grey.shade200;
        textColor = Colors.grey.shade600;
      } else {
        switch (user.status) {
          case BuddyStatus.accepted:
            buttonText = '已添加';
            isClickable = false;
            backgroundColor = Colors.grey.shade200;
            textColor = Colors.grey.shade600;
            break;
          case BuddyStatus.pending:
            buttonText = '待确认';
            isClickable = false;
            backgroundColor = Colors.grey.shade200;
            textColor = Colors.grey.shade600;
            break;
          case BuddyStatus.rejected:
            buttonText = '已拒绝';
            isClickable = false;
            backgroundColor = Colors.grey.shade200;
            textColor = Colors.grey.shade600;
            break;
          case BuddyStatus.limitReached:
            buttonText = '已满';
            isClickable = false;
            backgroundColor = Colors.grey.shade200;
            textColor = Colors.grey.shade600;
            break;
          case BuddyStatus.blocked:
            buttonText = '已屏蔽';
            isClickable = false;
            backgroundColor = Colors.grey.shade200;
            textColor = Colors.grey.shade600;
            break;
          default:
            // BuddyStatus.none 或 null - 可以添加
            if (!_canAddBuddy.value) {
              buttonText = '已满';
              isClickable = false;
              backgroundColor = Colors.grey.shade200;
              textColor = Colors.grey.shade600;
            } else {
              buttonText = '添加';
              isClickable = true;
              backgroundColor = null; // 使用渐变
              textColor = Colors.white;
            }
        }
      }

      return GestureDetector(
        onTap: isClickable ? () => _addBuddy(user) : null,
        child: Container(
          width: 68, // 减小宽度
          height: 32, // 减小高度
          decoration: BoxDecoration(
            color: isClickable && backgroundColor == null
                ? AppColors.primary
                : backgroundColor,
            borderRadius: BorderRadius.circular(16), // 调整圆角
            // 移除阴影效果
          ),
          child: isAdding
              ? const Center(
                  child: SizedBox(
                    width: 18,
                    height: 18,
                    child: CircularProgressIndicator(
                      color: Colors.grey,
                      strokeWidth: 2,
                    ),
                  ),
                )
              : Center(
                  child: Text(
                    buttonText,
                    style: TextStyle(
                      color: textColor,
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
        ),
      );
  }

  /// 获取状态描述文案
  String _getStatusDescription(BuddyStatus status) {
    switch (status) {
      case BuddyStatus.accepted:
        return '搭子关系已建立';
      case BuddyStatus.pending:
        return '等待对方确认';
      case BuddyStatus.rejected:
        return '申请被拒绝';
      case BuddyStatus.limitReached:
        return '搭子数量已满';
      case BuddyStatus.blocked:
        return '用户已屏蔽';
      default:
        return '';
    }
  }
}

/// 大写文本输入格式化器
class UpperCaseTextFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    return TextEditingValue(
      text: newValue.text.toUpperCase(),
      selection: newValue.selection,
    );
  }
}
