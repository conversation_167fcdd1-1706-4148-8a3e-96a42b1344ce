import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:life_habit_app/app/theme/app_colors.dart';
import 'package:life_habit_app/app/utils/customFonts.dart';

import '../modules/habit/habitDetail/controllers/habit_detail_controller.dart';

/// 基于 StatefulWidget 的响应式笔记对话框
class ReactiveNoteDialog extends StatefulWidget {
  final String habitTitle;
  final String? initialNote;
  final Function(String, int) onSave;
  final bool isEdit;
  final DateTime? noteTime;
  final List<String>? stageNames;
  final int selectedStageIndex;

  const ReactiveNoteDialog({
    Key? key,
    required this.habitTitle,
    this.initialNote,
    required this.onSave,
    this.isEdit = false,
    this.noteTime,
    this.stageNames,
    this.selectedStageIndex = 0,
  }) : super(key: key);

  @override
  State<ReactiveNoteDialog> createState() => _ReactiveNoteDialogState();

  // 显示对话框的静态方法
  static Future<String?> show({
    required BuildContext context,
    required String habitTitle,
    String? initialNote,
    required Function(String, int) onSave,
    bool isEdit = false,
    DateTime? noteTime,
    List<String>? stageNames,
    int selectedStageIndex = 0,
  }) {

    // 显示对话框
    final result = showModalBottomSheet<String>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Padding(
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        child: ReactiveNoteDialog(
          habitTitle: habitTitle,
          initialNote: initialNote,
          onSave: onSave,
          isEdit: isEdit,
          noteTime: noteTime,
          stageNames: stageNames,
          selectedStageIndex: selectedStageIndex,
        ),
      ),
    );
    
    return result;
  }
}

class _ReactiveNoteDialogState extends State<ReactiveNoteDialog>
    with TickerProviderStateMixin {

  // 动画控制器
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();

    // 初始化动画控制器
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    // 创建淡入淡出动画
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    // 启动入场动画
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// 带动画的关闭方法
  Future<void> _closeWithAnimation() async {
    // 执行淡出动画
    await _animationController.reverse();
    // 动画完成后关闭
    if (mounted) {
      Navigator.of(context).pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    // 使用GetX的懒加载方式获取控制器
    final HabitDetailController controller = Get.find<HabitDetailController>();

    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 360;
    final isTablet = screenSize.width > 600;

    final dialogWidth = isTablet
        ? 450.0
        : isSmallScreen
            ? screenSize.width * 0.95
            : screenSize.width * 0.9;

    final basePadding = isSmallScreen ? 24.0 : isTablet ? 48.0 : 24.0;

    // 格式化当前时间
    final now = widget.noteTime ?? DateTime.now();
    final timeString = '${_getDayOfWeek(now)}, ${_getMonthAbbr(now)} ${now.day} at ${_formatTime(now)}';

    return FadeTransition(
      opacity: _fadeAnimation,
      child: Container(
      width: double.infinity,
      color: Colors.transparent,
      child: GestureDetector(
        onTap: () => FocusScope.of(context).unfocus(),
        child: Center(
          child: Container(
            width: dialogWidth,
            height: (widget.stageNames?.isNotEmpty ?? false) ? 430 : 360,
            padding: EdgeInsets.symmetric(horizontal: basePadding),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: const BorderRadius.all(Radius.circular(16)),
              boxShadow: [
                BoxShadow(
                  offset: const Offset(0, -2),
                  blurRadius: 10,
                  color: Colors.black.withOpacity(0.1),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 顶部把手
                const SizedBox(height: 12),
                Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: const Color(0xFFE0E0E0),
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const SizedBox(height: 16),

                // 标题
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Icon(
                      CustomFonts.note,
                      color: AppColors.primary,
                      size: 18,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      widget.isEdit ? '编辑随记'.tr : '添加随记'.tr,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w500,
                        color: Color(0xFF333333),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),

                // 习惯名称
                Text(
                  widget.habitTitle,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF4B5563),
                  ),
                ),

                // 时间
                const SizedBox(height: 6),
                Text(
                  timeString,
                  style: const TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.w400,
                    color: Color(0xFF666666),
                  ),
                ),

                // 阶段选择器 (如果有阶段名称)
                if (widget.stageNames != null && widget.stageNames!.length > 1)
                  Padding(
                    padding: const EdgeInsets.only(top: 16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '选择阶段'.tr,
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: Color(0xFF4B5563),
                          ),
                        ),
                        const SizedBox(height: 8),
                        Container(
                          decoration: BoxDecoration(
                            color: const Color(0xFFF5FaF8),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            children: List.generate(
                              widget.stageNames!.length,
                              (index) => Expanded(
                                child: Obx(() {
                                  final isSelected = controller.selectedStageIndex.value == index;
                                  return GestureDetector(
                                    onTap: () => controller.selectedStageIndex.value = index,
                                    child: Container(
                                      padding: const EdgeInsets.symmetric(vertical: 8),
                                      decoration: BoxDecoration(
                                        color: isSelected
                                            ? Colors.white
                                            : Colors.transparent,
                                        borderRadius: BorderRadius.circular(6),
                                        boxShadow: isSelected
                                            ? [
                                                BoxShadow(
                                                  color: Colors.black.withOpacity(0.05),
                                                  blurRadius: 2,
                                                  offset: const Offset(0, 1),
                                                ),
                                              ]
                                            : null,
                                      ),
                                      alignment: Alignment.center,
                                      child: Text(
                                        widget.stageNames![index],
                                        style: TextStyle(
                                          fontSize: 14,
                                          fontWeight: FontWeight.w500,
                                          overflow: TextOverflow.ellipsis,
                                          color: isSelected
                                              ? const Color(0xFF1F2937)
                                              : const Color(0xFF6B7280),
                                        ),
                                      ),
                                    ),
                                  );
                                }),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                const SizedBox(height: 28),

                // 笔记输入区 - 使用性能优化的方式
                RepaintBoundary(
                  child: Container(
                    constraints: const BoxConstraints(
                      minHeight: 120,
                      maxHeight: 200,
                    ),
                    decoration: BoxDecoration(
                      color: const Color(0xFFFEFEFE),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: const Color(0xFFE9ECEF),
                        width: 1,
                      ),
                    ),
                    child: TextField(
                      maxLines: null,
                      keyboardType: TextInputType.multiline,
                      textInputAction: TextInputAction.newline,
                      style: const TextStyle(
                        fontSize: 16,
                        height: 1.4,
                        color: Color(0xFF333333),
                      ),
                      inputFormatters: [
                        LengthLimitingTextInputFormatter(500),
                      ],
                      scrollPhysics: const ClampingScrollPhysics(),
                      decoration: InputDecoration(
                        hintText: "请输入随记内容".tr,
                        hintStyle: const TextStyle(
                          color: Color(0xFF9E9E9E),
                        ),
                        contentPadding: const EdgeInsets.all(8),
                        border: InputBorder.none,
                      ),
                    ),
                  ),
                ),

                // 按钮区域
                Padding(
                  padding: const EdgeInsets.only(
                    top: 20,
                    bottom: 24,
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: TextButton(
                          onPressed: () => _closeWithAnimation(),
                          style: TextButton.styleFrom(
                            foregroundColor: const Color(0xFF464646),
                            backgroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 8),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                              side: const BorderSide(color: Color(0xFFE0E0E0)),
                            ),
                          ),
                          child: Text(
                            '取消'.tr,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 32),
                      Expanded(
                        child: Obx(() {
                          final canSave = true; // 允许保存
                          final isSaving = controller.isSaving.value;

                          return ElevatedButton(
                            onPressed: canSave && !isSaving ? () async {
                              // 调用保存回调
                              widget.onSave('', controller.selectedStageIndex.value);
                              // 使用动画关闭
                              await _closeWithAnimation();
                            } : null,
                            style: ElevatedButton.styleFrom(
                              foregroundColor: Colors.white,
                              backgroundColor: AppColors.primary,
                              padding: const EdgeInsets.symmetric(vertical: 8),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              elevation: 2,
                              shadowColor: Colors.black.withOpacity(0.15),
                              disabledBackgroundColor: AppColors.primary.withOpacity(0.6),
                            ),
                            child: isSaving
                                ? const SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(
                                      color: Colors.white,
                                      strokeWidth: 2,
                                    ),
                                  )
                                : Text(
                                    '保存'.tr,
                                    style: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w500,
                                      color: Colors.white,
                                    ),
                                  ),
                          );
                        }),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    ));
  }

  // 辅助函数：获取星期几
  String _getDayOfWeek(DateTime date) {
    final fullLocale = Get.locale.toString();
    final isEnglish = !fullLocale.startsWith('zh');
    
    switch (date.weekday) {
      case 1: return isEnglish ? '周一'.tr : '周一';
      case 2: return isEnglish ? '周二'.tr : '周二';
      case 3: return isEnglish ? '周三'.tr : '周三';
      case 4: return isEnglish ? '周四'.tr : '周四';
      case 5: return isEnglish ? '周五'.tr : '周五';
      case 6: return isEnglish ? '周六'.tr : '周六';
      case 7: return isEnglish ? '周日'.tr : '周日';
      default: return '';
    }
  }

  // 辅助函数：获取月份缩写
  String _getMonthAbbr(DateTime date) {
    final fullLocale = Get.locale.toString();
    final isEnglish = !fullLocale.startsWith('zh');
    
    switch (date.month) {
      case 1: return isEnglish ? '1月'.tr : '1月';
      case 2: return isEnglish ? '2月'.tr : '2月';
      case 3: return isEnglish ? '3月'.tr : '3月';
      case 4: return isEnglish ? '4月'.tr : '4月';
      case 5: return isEnglish ? '5月'.tr : '5月';
      case 6: return isEnglish ? '6月'.tr : '6月';
      case 7: return isEnglish ? '7月'.tr : '7月';
      case 8: return isEnglish ? '8月'.tr : '8月';
      case 9: return isEnglish ? '9月'.tr : '9月';
      case 10: return isEnglish ? '10月'.tr : '10月';
      case 11: return isEnglish ? '11月'.tr : '11月';
      case 12: return isEnglish ? '12月'.tr : '12月';
      default: return '';
    }
  }

  // 辅助函数：格式化时间
  String _formatTime(DateTime date) {
    final fullLocale = Get.locale.toString();
    final isEnglish = !fullLocale.startsWith('zh');
    
    if (isEnglish) {
      final hour = date.hour > 12 ? date.hour - 12 : date.hour == 0 ? 12 : date.hour;
      final minute = date.minute.toString().padLeft(2, '0');
      final period = date.hour >= 12 ? 'PM'.tr : 'AM'.tr;
      return '$hour:$minute $period';
    } else {
      // 中文习惯使用24小时制
      final hour = date.hour.toString().padLeft(2, '0');
      final minute = date.minute.toString().padLeft(2, '0');
      return '$hour:$minute';
    }
  }
}
