import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:life_habit_app/app/theme/app_colors.dart';
import 'package:life_habit_app/app/utils/customFonts.dart';

import '../utils/hi_constants.dart';
import '../utils/toast.dart';

class HabitNoteDialog extends StatefulWidget {
  final String habitTitle;
  final String? initialNote;
  final Function(String, int) onSave;
  final bool isEdit;
  final DateTime? noteTime;
  final List<String>? stageNames;
  final int selectedStageIndex;

  const HabitNoteDialog({
    super.key,
    required this.habitTitle,
    this.initialNote,
    required this.onSave,
    this.isEdit = false,
    this.noteTime,
    this.stageNames,
    this.selectedStageIndex = 0,
  });

  static Future<String?> show({
    required BuildContext context,
    required String habitTitle,
    String? initialNote,
    required Function(String, int) onSave,
    bool isEdit = false,
    DateTime? noteTime,
    List<String>? stageNames,
    int selectedStageIndex = 0,
  }) {
    return showModalBottomSheet<String>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      enableDrag: true,
      isDismissible: true,
      useSafeArea: true,
      builder: (context) => AnimatedPadding(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        child: HabitNoteDialog(
          habitTitle: habitTitle,
          initialNote: initialNote,
          onSave: onSave,
          isEdit: isEdit,
          noteTime: noteTime,
          stageNames: stageNames,
          selectedStageIndex: selectedStageIndex,
        ),
      ),
    );
  }

  @override
  _HabitNoteDialogState createState() => _HabitNoteDialogState();
}

class _HabitNoteDialogState extends State<HabitNoteDialog>
    with TickerProviderStateMixin {
  late TextEditingController _noteController;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  bool _isSaving = false;
  late int _selectedStageIndex;
  bool _hasText = false; // 添加文本状态跟踪

  @override
  void initState() {
    super.initState();
    _noteController = TextEditingController(text: widget.initialNote);
    _selectedStageIndex = widget.selectedStageIndex;
    _hasText = widget.initialNote?.trim().isNotEmpty ?? false;

    // 添加文本监听器
    _noteController.addListener(_onTextChanged);

    // 初始化动画控制器
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    // 启动入场动画
    _animationController.forward();
  }

  void _onTextChanged() {
    final hasText = _noteController.text.trim().isNotEmpty;
    if (hasText != _hasText) {
      setState(() {
        _hasText = hasText;
      });
    }
  }

  @override
  void dispose() {
    _noteController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  /// 带动画的关闭方法
  Future<void> _closeWithAnimation() async {
    // 执行淡出动画
    await _animationController.reverse();
    // 动画完成后关闭
    if (mounted) {
      Navigator.of(context).pop();
    }
  }

  Future<void> _saveNote() async {
    if (_noteController.text.trim().isEmpty) {
      showErrorMessage(HiConstants.errorTypeWarning, "请输入随记内容".tr);
      return;
    }

    setState(() {
      _isSaving = true;
    });

    try {
      widget.onSave(_noteController.text.trim(), _selectedStageIndex);
      await _closeWithAnimation();
    } finally {
      setState(() {
        _isSaving = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 360;
    final isTablet = screenSize.width > 600;

    final basePadding = isSmallScreen
        ? 16.0
        : isTablet
            ? 32.0
            : 20.0;

    // 格式化当前时间
    final now = widget.noteTime ?? DateTime.now();
    final timeString =
        '${_getDayOfWeek(now)}, ${_getMonthAbbr(now)} ${now.day} at ${_formatTime(now)}';
    var stageNames = <String>[];
    if (widget.stageNames != null) {
      stageNames = widget.stageNames!;
    }

    return FadeTransition(
      opacity: _fadeAnimation,
      child: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () {
          // 点击空白区域时取消焦点
          FocusScope.of(context).unfocus();
        },
        child: AnimatedBuilder(
        animation: _fadeAnimation,
        builder: (context, child) => Transform.translate(
          offset: Offset(0, (1 - _fadeAnimation.value) * 20),
          child: Opacity(
            opacity: _fadeAnimation.value,
            child: Container(
              width: double.infinity,
              constraints: BoxConstraints(
                maxHeight: MediaQuery.of(context).size.height * 0.85,
              ),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius:
                    const BorderRadius.vertical(top: Radius.circular(20)),
                boxShadow: [
                  BoxShadow(
                    offset: const Offset(0, -4),
                    blurRadius: 20,
                    color: Colors.black.withOpacity(0.15),
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // 顶部把手
                  const SizedBox(height: 12),
                  Container(
                    width: 40,
                    height: 4,
                    decoration: BoxDecoration(
                      color: const Color(0xFFE0E0E0),
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),

                  // 可滚动内容区域
                  Flexible(
                    child: SingleChildScrollView(
                      padding: EdgeInsets.symmetric(horizontal: basePadding),
                      physics: const ClampingScrollPhysics(),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const SizedBox(height: 16),

                          // 标题
                          Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Icon(
                                CustomFonts.note,
                                color: AppColors.primary,
                                size: 18,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                widget.isEdit ? '编辑随记'.tr : '添加随记'.tr,
                                style: const TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.w500,
                                  color: Color(0xFF333333),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),

                          // 习惯名称
                          Text(
                            widget.habitTitle,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: Color(0xFF4B5563),
                            ),
                          ),

                          // 时间
                          const SizedBox(height: 6),
                          Text(
                            timeString,
                            style: const TextStyle(
                              fontSize: 13,
                              fontWeight: FontWeight.w400,
                              color: Color(0xFF666666),
                            ),
                          ),

                          // 阶段选择器 (如果有阶段名称)
                          if (stageNames.length > 1)
                            Padding(
                              padding: const EdgeInsets.only(top: 16),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    '选择阶段'.tr,
                                    style: const TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                      color: Color(0xFF4B5563),
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  Container(
                                    decoration: BoxDecoration(
                                      color: const Color(0xFFF5FaF8),
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: Row(
                                      children: List.generate(
                                        stageNames.length,
                                        (index) => Expanded(
                                          child: GestureDetector(
                                            onTap: () {
                                              setState(() {
                                                _selectedStageIndex = index;
                                              });
                                            },
                                            child: Container(
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                      vertical: 8),
                                              decoration: BoxDecoration(
                                                color:
                                                    _selectedStageIndex == index
                                                        ? Colors.white
                                                        : Colors.transparent,
                                                borderRadius:
                                                    BorderRadius.circular(6),
                                                boxShadow:
                                                    _selectedStageIndex == index
                                                        ? [
                                                            BoxShadow(
                                                              color: Colors
                                                                  .black
                                                                  .withOpacity(
                                                                      0.05),
                                                              blurRadius: 2,
                                                              offset:
                                                                  const Offset(
                                                                      0, 1),
                                                            ),
                                                          ]
                                                        : null,
                                              ),
                                              alignment: Alignment.center,
                                              child: Text(
                                                stageNames[index],
                                                style: TextStyle(
                                                  fontSize: 14,
                                                  fontWeight: FontWeight.w500,
                                                  overflow:
                                                      TextOverflow.ellipsis,
                                                  color: _selectedStageIndex ==
                                                          index
                                                      ? const Color(0xFF1F2937)
                                                      : const Color(0xFF6B7280),
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),

                          const SizedBox(height: 28),

                          // 笔记输入区
                          GestureDetector(
                            onTap: () {
                              // 阻止事件冒泡到父级GestureDetector
                            },
                            child: Container(
                              constraints: const BoxConstraints(
                                minHeight: 120,
                                maxHeight: 200,
                              ),
                              decoration: BoxDecoration(
                                color: const Color(0xFFFEFEFE),
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color: const Color(0xFFE9ECEF),
                                  // border-[hsl(var(--habit-border))]
                                  width: 1, // 1px边框
                                ),
                              ),
                              child: TextField(
                                controller: _noteController,
                                maxLines: null,
                                autofocus: false,
                                // 避免立即弹出键盘
                                textInputAction: TextInputAction.newline,
                                style: const TextStyle(
                                  fontSize: 16,
                                  height: 1.4,
                                  color: Color(0xFF333333),
                                ),
                                inputFormatters: [
                                  LengthLimitingTextInputFormatter(500),
                                ],
                                decoration: InputDecoration(
                                  hintText: '在这里输入内容...'.tr,
                                  hintStyle: const TextStyle(
                                    color: Color(0xFF9E9E9E),
                                  ),
                                  contentPadding: const EdgeInsets.all(12),
                                  border: InputBorder.none,
                                ),
                              ),
                            ),
                          ),

                          const SizedBox(height: 20),
                        ],
                      ),
                    ),
                  ),

                  // 固定底部按钮区域
                  Container(
                    padding: EdgeInsets.fromLTRB(basePadding, 20, basePadding,
                        MediaQuery.of(context).padding.bottom + 20),
                    decoration: BoxDecoration(
                      color: Colors.white,
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: TextButton(
                            onPressed: () => _closeWithAnimation(),
                            style: TextButton.styleFrom(
                              foregroundColor: const Color(0xFF464646),
                              backgroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                                side:
                                    const BorderSide(color: Color(0xFFE0E0E0)),
                              ),
                            ),
                            child: Text(
                              '取消'.tr,
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: !_hasText || _isSaving ? null : _saveNote,
                            style: ElevatedButton.styleFrom(
                              foregroundColor: Colors.white,
                              backgroundColor: AppColors.primary,
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              elevation: 2,
                              shadowColor: Colors.black.withOpacity(0.15),
                              disabledBackgroundColor:
                                  AppColors.primary.withOpacity(0.6),
                            ),
                            child: _isSaving
                                ? const SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(
                                      color: Colors.white,
                                      strokeWidth: 2,
                                    ),
                                  )
                                : Text(
                                    '保存'.tr,
                                    style: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w500,
                                      color: Colors.white,
                                    ),
                                  ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    ));
  }

  // 辅助函数：获取星期几
  String _getDayOfWeek(DateTime date) {
    final fullLocale = Get.locale.toString();
    final isEnglish = !fullLocale.startsWith('zh');

    switch (date.weekday) {
      case 1:
        return isEnglish ? '周一'.tr : '周一';
      case 2:
        return isEnglish ? '周二'.tr : '周二';
      case 3:
        return isEnglish ? '周三'.tr : '周三';
      case 4:
        return isEnglish ? '周四'.tr : '周四';
      case 5:
        return isEnglish ? '周五'.tr : '周五';
      case 6:
        return isEnglish ? '周六'.tr : '周六';
      case 7:
        return isEnglish ? '周日'.tr : '周日';
      default:
        return '';
    }
  }

  // 辅助函数：获取月份缩写
  String _getMonthAbbr(DateTime date) {
    final fullLocale = Get.locale.toString();
    final isEnglish = !fullLocale.startsWith('zh');

    switch (date.month) {
      case 1:
        return isEnglish ? '1月'.tr : '1月';
      case 2:
        return isEnglish ? '2月'.tr : '2月';
      case 3:
        return isEnglish ? '3月'.tr : '3月';
      case 4:
        return isEnglish ? '4月'.tr : '4月';
      case 5:
        return isEnglish ? '5月'.tr : '5月';
      case 6:
        return isEnglish ? '6月'.tr : '6月';
      case 7:
        return isEnglish ? '7月'.tr : '7月';
      case 8:
        return isEnglish ? '8月'.tr : '8月';
      case 9:
        return isEnglish ? '9月'.tr : '9月';
      case 10:
        return isEnglish ? '10月'.tr : '10月';
      case 11:
        return isEnglish ? '11月'.tr : '11月';
      case 12:
        return isEnglish ? '12月'.tr : '12月';
      default:
        return '';
    }
  }

  // 辅助函数：格式化时间
  String _formatTime(DateTime date) {
    final fullLocale = Get.locale.toString();
    final isEnglish = !fullLocale.startsWith('zh');

    if (isEnglish) {
      final hour = date.hour > 12
          ? date.hour - 12
          : date.hour == 0
              ? 12
              : date.hour;
      final minute = date.minute.toString().padLeft(2, '0');
      final period = date.hour >= 12 ? 'PM'.tr : 'AM'.tr;
      return '$hour:$minute $period';
    } else {
      // 中文习惯使用24小时制
      final hour = date.hour.toString().padLeft(2, '0');
      final minute = date.minute.toString().padLeft(2, '0');
      return '$hour:$minute';
    }
  }
}
