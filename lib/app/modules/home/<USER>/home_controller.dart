import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:life_habit_app/app/utils/hi_net.dart';
import 'package:life_habit_app/app/utils/cacheImage.dart';
import 'package:life_habit_app/app/widget/celebration_preview_dialog.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../data/dao/login_dao.dart';
import '../../../data/dao/user_dao.dart';
import '../../../data/dao/user_habit_dao.dart';
import '../../../models/user_habit_snapshot_model.dart';
import '../../../utils/enums.dart';
import '../../../utils/tool.dart';
import '../../../widget/custom_note_dialog.dart';

class HomeController extends GetxController with LoadingMixin {
  final ValueNotifier<double> smallHabitValueNotifier = ValueNotifier(0);
  final ValueNotifier<double> normalHabitValueNotifier = ValueNotifier(0);

  // 习惯数据列表
  var topHabits = <HabitItem>[].obs; // 重要习惯列表
  var lowHabits = <HabitItem>[].obs; // 需要关注习惯列表
  var otherHabits = <HabitItem>[].obs; // 其他习惯列表

  // 筛选标签状态
  var activeTab = labelUndo.obs;

  // Data for today's statistics
  var todayStatisticData = TodayStatisticData(
    smallHabit: SmallHabit(per: 0, doneCount: 0, allCount: 0),
    normalHabit: SmallHabit(per: 0, doneCount: 0, allCount: 0),
    weekData: [
      WeekData(day: "日", progress: "--", isToday: false),
      WeekData(day: "一", progress: "--", isToday: false),
      WeekData(day: "二", progress: "--", isToday: false),
      WeekData(day: "三", progress: "--", isToday: false),
      WeekData(day: "四", progress: "--", isToday: false),
      WeekData(day: "五", progress: "--", isToday: false),
      WeekData(day: "六", progress: "--", isToday: false),
    ],
  ).obs;

  // 计时器相关状态
  RxString activeTimerId = RxString(''); // 当前活动计时器的ID
  RxMap<String, Duration> timerDurations =
      RxMap<String, Duration>(); // 存储各计时器的时长
  RxMap<String, bool> timerActive = RxMap<String, bool>(); // 存储各计时器的活动状态
  Timer? timer; // 计时器实例

  // 笔记相关状态
  RxMap<String, String> habitNotes = RxMap<String, String>(); // 存储各习惯的笔记

  // 左滑状态管理
  RxString openedActionId = RxString(''); // 当前打开操作区的卡片ID

  // 记录已展开的习惯ID
  final RxSet<String> _expandedHabits = <String>{}.obs;

  Set<String> get expandedHabits => _expandedHabits;

  // Variables for motivation settings
  var imageUrl = "";
  var cacheImageKey = "";
  var unPunchedCount = 0;

  // 激励弹窗相关状态
  final RxSet<int> _pendingMotivationHabits = <int>{}.obs; // 待完成的激励习惯ID
  final RxBool _hasShownMotivationToday = false.obs; // 今日是否已显示激励弹窗
  final RxBool _isMotivationEnabled = false.obs; // 是否启用了激励功能
  final RxBool _isNeedShowMotivation = false.obs; // 后端返回的是否需要显示激励弹窗标识

  // 防重复加载状态
  bool _isInitializing = false;
  bool _hasInitialized = false;

  @override
  void onInit() {
    super.onInit();
    // 🔥 优化：在onInit阶段就开始初始化计时器和数据加载
    startTimerUpdates();
    // 立即开始数据初始化，不等待onReady
    Future.microtask(() => _initializeDataIfNeeded());
  }

  @override
  void onReady() {
    super.onReady();
    // onReady时确保数据已经开始加载
    _initializeDataIfNeeded();
  }

  @override
  void onClose() {
    // 清理计时器资源
    timer?.cancel();
    super.onClose();
  }

  // 如果需要的话初始化数据
  Future<void> _initializeDataIfNeeded() async {
    if (_hasInitialized || _isInitializing) return;

    _isInitializing = true;
    try {
      await refreshHabitData();
      _hasInitialized = true;
    } finally {
      _isInitializing = false;
    }
  }

  // 检查是否已经初始化过数据
  bool get hasInitialized => _hasInitialized;

  // 启动计时器更新循环
  void startTimerUpdates() {
    timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      // 更新所有活动的计时器，而不仅仅是activeTimerId对应的计时器
      bool hasActiveTimer = false;

      for (final entry in timerActive.entries) {
        final timerId = entry.key;
        final isActive = entry.value;

        if (isActive == true) {
          final currentDuration = timerDurations[timerId] ?? Duration.zero;
          timerDurations[timerId] =
              currentDuration + const Duration(seconds: 1);
          hasActiveTimer = true;

          // 确保activeTimerId指向一个活动的计时器
          if (activeTimerId.value.isEmpty ||
              timerActive[activeTimerId.value] != true) {
            activeTimerId.value = timerId;
          }
        }
      }

      // 如果没有活动的计时器，清空activeTimerId
      if (!hasActiveTimer) {
        activeTimerId.value = '';
      }
    });
  }

  // 切换计时器状态
  Future<void> toggleTimer(String habitId) async {
    // 初始化当前习惯的计时器（如果不存在）
    if (!timerDurations.containsKey(habitId)) {
      timerDurations[habitId] = Duration.zero;
      timerActive[habitId] = false;
    }

    // 切换计时状态
    timerActive[habitId] = !(timerActive[habitId] ?? false);

    // 更新活动计时器ID
    if (timerActive[habitId] == true) {
      activeTimerId.value = habitId;
    } else if (activeTimerId.value == habitId) {
      activeTimerId.value = '';
    }
  }

  // 格式化计时器显示
  String formatTimerDuration(String habitId) {
    final duration = timerDurations[habitId] ?? Duration.zero;
    final minutes = duration.inMinutes.remainder(60).toString().padLeft(2, '0');
    final seconds = duration.inSeconds.remainder(60).toString().padLeft(2, '0');
    return '$minutes:$seconds';
  }

  // 重置计时器
  void resetTimer(String habitId) {
    timerDurations[habitId] = Duration.zero;
    if (activeTimerId.value == habitId) {
      timerActive[habitId] = false;
      activeTimerId.value = '';
    }
  }

  // 打开笔记对话框
  Future<void> openNoteDialog(BuildContext context, int habitId, habitTitle,
      {int smallStageID = 0}) async {
    HabitNoteDialog.show(
      context: context,
      habitTitle: habitTitle,
      onSave: (newNote, smallStageID) {
        // 保存笔记的逻辑
        createMemo(habitId, newNote, smallStageID: smallStageID);
        update(); // GetX刷新UI
      },
    );
  }

  // 切换左滑操作区
  void toggleActionMenu(String habitId) {
    if (openedActionId.value == habitId) {
      openedActionId.value = ''; // 关闭当前打开的操作区
    } else {
      openedActionId.value = habitId; // 打开新的操作区
    }
  }

  // 关闭所有操作区
  void closeAllActionMenus() {
    openedActionId.value = '';
  }

  // 切换习惯展开状态
  void toggleHabitExpanded(String habitId) {
    if (_expandedHabits.contains(habitId)) {
      _expandedHabits.remove(habitId);
    } else {
      _expandedHabits.add(habitId);
    }
  }

  // 设置活动标签
  void setActiveTab(String tab) {
    // 确保输入的tab值有效
    if ([labelAll, labelDone, labelUndo].contains(tab)) {
      activeTab.value = tab;
      // 关闭任何打开的操作菜单
      closeAllActionMenus();
      // 刷新数据以应用筛选
      refreshHabitData();
    }
  }

  Future<void> refreshHabitData() async {
    // 重置错误状态
    loadingController.resetError();

    await loadingController.fetchData(() async {
      // 初始化激励状态
      _initMotivationState();
      await listHabitData();
    });
  }

  // 刷新习惯数据
  Future<void> listHabitData() async {
    // 显示加载状态
    String currentDate = getLocalTime();
    var labelType = labelMap[activeTab.value] ?? labelMap[labelAll];
    var result = await UserHabitsDao().listSnapshot(labelType!, currentDate);

    // 清空现有数据
    topHabits.clear();
    lowHabits.clear();
    otherHabits.clear();

    topHabits.value = result.topHabits!;
    lowHabits.value = result.lowHabits!;
    otherHabits.value = result.trackHabits!;

    // 更新统计数据
    todayStatisticData.value = result.todayStatisticData!;

    // 从后端获取是否需要显示激励弹窗的标识
    _isNeedShowMotivation.value = result.isNeedShow ?? false;

    // 直接从接口获取待完成的激励习惯ID列表并缓存
    _pendingMotivationHabits.clear();
    if (result.pendingMotivationHabits != null) {
      _pendingMotivationHabits.addAll(result.pendingMotivationHabits!);
    }
    _isMotivationEnabled.value = imageUrl.isNotEmpty;

    // 单次遍历处理所有习惯相关逻辑（性能优化）
    _processHabitsInSinglePass();

    // 在帧绘制完成后更新值
    WidgetsBinding.instance.addPostFrameCallback((_) {
      smallHabitValueNotifier.value =
          todayStatisticData.value.smallHabit!.per!.toDouble();
      normalHabitValueNotifier.value =
          todayStatisticData.value.normalHabit!.per!.toDouble();

      // 检查是否应该显示激励弹窗
      _checkAndShowMotivation();

      // 保存激励状态到本地缓存
      _saveMotivationState();
    });
  }

  // 单次遍历处理所有习惯相关逻辑（性能优化）
  void _processHabitsInSinglePass() {
    // 收集所有习惯
    final allHabits = [...topHabits, ...lowHabits, ...otherHabits];

    // 计时器相关变量
    Set<String> shouldBeActiveTimerIds = {};
    String? newActiveTimerId;

    // 单次遍历处理计时器逻辑
    for (final habit in allHabits) {
      final habitId = habit.id!.toString();

      // === 计时器恢复逻辑 ===
      // 检查普通习惯的计时状态
      if (habit.isReckoning == true &&
          habit.duration != null &&
          habit.duration! > 0) {
        shouldBeActiveTimerIds.add(habitId);
        if (newActiveTimerId == null) {
          newActiveTimerId = habitId;
          _restoreHabitTimerWithoutActiveId(habitId, habit.duration!);
        }
      }

      // 检查微习惯的子阶段计时状态
      if (habit.smallStages != null) {
        for (final stage in habit.smallStages!) {
          final stageKey = '$habitId-${stage.stageId}';
          if (stage.isReckoning == true) {
            shouldBeActiveTimerIds.add(stageKey);
            if (newActiveTimerId == null) {
              newActiveTimerId = stageKey;
              _restoreStageTimerWithoutActiveId(stageKey, 0);
            }
          }
        }
      }
    }

    // 完成计时器状态更新
    activeTimerId.value = newActiveTimerId ?? '';
    _removeInactiveTimers(shouldBeActiveTimerIds, newActiveTimerId);
  }

  // 移除不再活动的计时器
  void _removeInactiveTimers(
      Set<String> shouldBeActiveTimerIds, String? currentActiveTimerId) {
    // 获取所有当前存在的计时器ID
    final currentTimerIds = Set<String>.from(timerActive.keys);

    // 找出需要移除的计时器（当前存在但不应该活动的）
    final timersToRemove = currentTimerIds.difference(shouldBeActiveTimerIds);

    for (final timerId in timersToRemove) {
      // 如果这个计时器当前是活动状态，需要移除
      if (timerActive[timerId] == true) {
        timerActive[timerId] = false;
        timerDurations.remove(timerId);
        timerActive.remove(timerId);

        // 如果这是当前活动的计时器，清除活动ID
        if (activeTimerId.value == timerId) {
          activeTimerId.value = '';
        }
      }
    }

    // 清理那些应该活动但不是当前活动计时器的项目
    // 使用传入的currentActiveTimerId而不是activeTimerId.value
    for (final timerId in shouldBeActiveTimerIds) {
      if (timerActive[timerId] == true && timerId != currentActiveTimerId) {
        timerActive[timerId] = false;
        timerDurations.remove(timerId);
        timerActive.remove(timerId);
      }
    }
  }

  // 恢复习惯计时器（不更新activeTimerId）
  void _restoreHabitTimerWithoutActiveId(
      String habitId, int durationInSeconds) {
    timerDurations[habitId] = Duration(seconds: durationInSeconds);
    timerActive[habitId] = true;
  }

  // 恢复阶段计时器（不更新activeTimerId）
  void _restoreStageTimerWithoutActiveId(
      String stageKey, int durationInSeconds) {
    timerDurations[stageKey] = Duration(seconds: durationInSeconds);
    timerActive[stageKey] = true;
  }

  Future<void> punchHabit(int habitID, {int smallStageID = 0}) async {
    String currentDate = getLocalTime();

    await UserHabitsDao.punch(habitID, currentDate, smallStageID);

    // 刷新数据
    await refreshHabitData();
  }

  Future<void> cancelPunchHabit(int habitID, {int smallStageID = 0}) async {
    String currentDate = getLocalTime();

    await UserHabitsDao.cancelPunch(habitID,
        currentDate: currentDate, smallStageID: smallStageID);

    refreshHabitData();
  }

  // Start timing a habit
  Future<void> reckonUserHabit(int habitID,
      {int duration = 0, bool isEnd = false, int smallStageID = 0}) async {
    String currentDate = getLocalTime();
    await UserHabitsDao.reckon(
        habitID, currentDate, smallStageID, duration, isEnd);

    // 如果是结束计时，刷新数据
    if (isEnd) {
      await refreshHabitData();
    }
  }

  // Cancel timing for a habit
  Future cancelReckonUserHabit(int habitID, {int smallStageID = 0}) async {
    String currentDate = getLocalTime();
    await UserHabitsDao.cancelReckon(habitID, currentDate, smallStageID);
  }

  // Create a memo for a habit
  Future createMemo(int habitID, String memo, {int smallStageID = 0}) async {
    String currentDate = getLocalTime();
    await UserHabitsDao.createMemo(habitID, memo, currentDate, smallStageID);
  }

  // Fetch user settings
  Future<void> getUserSetting() async {
    var userID = LoginDao.getUserId();
    if (userID == 0) {
      return;
    }
    var userSettingData =
        await UserInfoDao.getUserSetting(scene: userSettingSceneHomePage);
    imageUrl = userSettingData.awardImageUrlWithDomain;
    cacheImageKey = userSettingData.awardImageUrlWithDomain;
    int imageMarkIndex = cacheImageKey.indexOf('?');
    if (imageMarkIndex != -1) {
      cacheImageKey = cacheImageKey.substring(0, imageMarkIndex);
    }
  }

  // Initialize motivation state
  Future<void> _initMotivationState() async {
    await _loadMotivationState();
    await getUserSetting();
  }

  // 加载激励状态（每日重置逻辑）
  Future<void> _loadMotivationState() async {
    final prefs = await SharedPreferences.getInstance();
    final today = DateTime.now().toIso8601String().split('T')[0];
    final lastMotivationDate = prefs.getString('last_motivation_date') ?? '';

    if (lastMotivationDate == today) {
      // 如果是今天，加载已有状态（不包括 pendingMotivationHabits，因为这个从接口获取）
      _hasShownMotivationToday.value =
          prefs.getBool('has_shown_motivation_today') ?? false;
    } else {
      // 如果是新的一天，重置状态
      _hasShownMotivationToday.value = false;
      _pendingMotivationHabits.clear();
      await prefs.setString('last_motivation_date', today);
      await prefs.setBool('has_shown_motivation_today', false);
      await prefs.setString('pending_motivation_habits', '');
    }
  }

  // 保存激励状态
  Future<void> _saveMotivationState() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(
        'has_shown_motivation_today', _hasShownMotivationToday.value);
    final pendingHabitsString = _pendingMotivationHabits.join(',');
    await prefs.setString('pending_motivation_habits', pendingHabitsString);
  }

  // 检查并显示激励弹窗
  void _checkAndShowMotivation() async {
    // 如果今日已显示过激励弹窗，则不再显示
    if (_hasShownMotivationToday.value) {
      return;
    }

    // 如果没有启用激励功能，则不显示
    if (!_isMotivationEnabled.value) {
      return;
    }

    // 如果后端标识不需要显示激励弹窗，则不显示
    if (!_isNeedShowMotivation.value) {
      return;
    }

    // 如果还有未完成的激励习惯，则不显示
    if (_pendingMotivationHabits.isNotEmpty) {
      return;
    }

    // 所有条件满足，显示激励弹窗
    _showMotivationDialog();
  }

  // 显示激励弹窗
  void _showMotivationDialog() {
    if (imageUrl.isEmpty) return;

    // 标记今日已显示
    _hasShownMotivationToday.value = true;
    _saveMotivationState();

    // 延迟一点时间显示，确保页面已经渲染完成
    Future.delayed(const Duration(milliseconds: 500), () {
      if (Get.context != null) {
        showDialog(
          context: Get.context!,
          barrierDismissible: true,
          barrierColor: Colors.black.withOpacity(0.8),
          builder: (context) => CelebrationPreviewDialog(
            imageWidget: cachedImage(
              imageUrl,
              cacheKey: cacheImageKey,
              cacheManager: CustomPermanentCacheManager.instance,
            ),
          ),
        );
      }
    });
  }

  // 激励状态相关的 getter 方法

  /// 获取待完成的激励习惯数量
  int get pendingMotivationHabitsCount => _pendingMotivationHabits.length;

  /// 获取后端返回的是否需要显示激励弹窗标识
  bool get isNeedShowMotivation => _isNeedShowMotivation.value;

  /// 获取待完成的激励习惯ID列表
  Set<int> get pendingMotivationHabits => Set.from(_pendingMotivationHabits);

  /// 检查今日是否已显示过激励弹窗
  bool get hasShownMotivationToday => _hasShownMotivationToday.value;

  /// 检查是否启用了激励功能
  bool get isMotivationEnabled => _isMotivationEnabled.value;

  /// 手动触发激励检查（用于调试或特殊情况）
  void manualCheckMotivation() {
    // 通过刷新数据来触发激励检查
    refreshHabitData();
  }

  /// 重置今日激励状态（用于调试）
  Future<void> resetMotivationToday() async {
    _hasShownMotivationToday.value = false;
    _isNeedShowMotivation.value = false;
    _pendingMotivationHabits.clear();
    await _saveMotivationState();
  }
}
