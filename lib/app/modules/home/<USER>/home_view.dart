import 'package:dashed_circular_progress_bar/dashed_circular_progress_bar.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:intl/intl.dart';
import 'package:life_habit_app/app/utils/toast.dart';
import '../../../models/user_habit_snapshot_model.dart';
import '../../../routes/app_pages.dart';
import '../../../utils/customFonts.dart';
import '../../../utils/enums.dart';
import '../../../utils/hi_constants.dart';
import '../../../utils/screenAdapter.dart';
import '../../../utils/tool.dart';
import '../../../widget/custom_filter_tabs.dart';
import '../../../widget/to_refresh.dart';
import '../controllers/home_controller.dart';
import 'package:life_habit_app/app/theme/app_colors.dart';
import 'package:flutter_slidable/flutter_slidable.dart';

class HomeView extends GetView<HomeController> {
  const HomeView({super.key});

  @override
  Widget build(BuildContext context) {
    // 获取当前日期
    final fullLocale = Localizations.localeOf(context).toString();
    final now = DateTime.now();
    final formattedDate = DateFormat('EEEE, MMMM d', fullLocale).format(now);

    return Scaffold(
      appBar: AppBar(
        toolbarHeight: 0,
      ),
      body: SafeArea(
        child: Center(
          child: Container(
            constraints: const BoxConstraints(maxWidth: 512), // max-w-lg
            padding: const EdgeInsets.all(16),
            color: AppColors.background,
            child: Column(
              children: [
                // 固定区域 - 不参与滚动
                // 顶部日期区
                _buildDateHeader(formattedDate),

                // 可滚动区域 - 包含统计卡片、筛选标签和习惯列表
                Expanded(
                  child: NotificationListener<ScrollNotification>(
                    onNotification: (scrollNotification) {
                      // 可以在这里监听滚动状态，如有需要
                      return false;
                    },
                    child: RefreshIndicator(
                      onRefresh: () async {
                        // 实现刷新逻辑
                        await controller.refreshHabitData();
                      },
                      color: AppColors.primary,
                      backgroundColor: const Color(0xFFF5FaF8),
                      // 使用CustomScrollView和SliverAppBar来实现吸顶效果
                      child: Obx(() {
                        // 检查是否有网络错误
                        if (controller.loadingController.isError.value) {
                          return _buildErrorState();
                        }

                        return CustomScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          slivers: [
                            // 统计卡片区 - 可滚动
                            SliverToBoxAdapter(
                              child: _buildStatisticsCard(),
                            ),

                            // 筛选标签区 - 滚动到顶部时固定
                            SliverPersistentHeader(
                              pinned: true, // 设置为true以实现吸顶效果
                              delegate: _SliverAppBarDelegate(
                                minHeight: 60, // 最小高度
                                maxHeight: 60, // 最大高度
                                child: _buildFilterTabs(),
                              ),
                            ),

                            // 习惯列表区域
                            SliverList(
                              delegate: SliverChildListDelegate([
                                // Top Habits区域
                                if (controller.topHabits.isNotEmpty)
                                  _buildTopHabitsSection(context),

                                // Low Habits区域
                                if (controller.lowHabits.isNotEmpty)
                                  _buildLowHabitsSection(context),

                                // Other Habits区域
                                _buildOtherHabitsSection(context),

                                // 底部间距
                                const SizedBox(height: 32),
                              ]),
                            ),
                          ],
                        );
                      }),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // 顶部日期区
  Widget _buildDateHeader(String formattedDate) {
    return Container(
      height: 44, // 固定高度44px
      margin: const EdgeInsets.only(bottom: 16.0), // mb-4
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center, // 垂直居中对齐
        children: [
          Text(
            formattedDate,
            style: const TextStyle(
              fontSize: 20, // text-xl
              fontWeight: FontWeight.bold, // font-bold (700)
              color: Color(0xFF1F2937), // text-gray-800
              height: 1.2, // 紧凑行高
            ),
            overflow: TextOverflow.visible, // 不截断，允许自然换行
          ),
          Container(
            height: 40, // h-10
            width: 40, // w-10
            decoration: BoxDecoration(
              color: AppColors.primary, // bg-primary
              shape: BoxShape.circle, // rounded-full
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 2,
                  offset: const Offset(0, 1),
                ), // 轻微阴影 shadow-sm
              ],
            ),
            child: Material(
              color: Colors.transparent,
              shape: const CircleBorder(),
              clipBehavior: Clip.hardEdge,
              child: InkWell(
                onTap: () {
                  // 添加习惯的操作
                  Get.toNamed(Routes.HABIT_ADD);
                },
                child: Center(
                  child: Icon(
                    CustomFonts.addWithoutBorder,
                    size: 20, // h-5 w-5
                    color: Colors.white,
                    weight: 2.0, // 2px的线条粗细
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 统计卡片区
  Widget _buildStatisticsCard() {
    return Container(
      margin: const EdgeInsets.only(bottom: 12.0), // mb-6
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8), // p-4
      decoration: BoxDecoration(
        color: const Color(0xFFF5FaF8), // bg-[hsl(var(--habit-surface))]
        border: Border.all(
          color: const Color(0xFFE9ECEF), // border-[hsl(var(--habit-border))]
          width: 1, // 1px边框
        ),
        borderRadius: BorderRadius.circular(8.0), // rounded-lg
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.02),
            blurRadius: 2,
            offset: const Offset(0, 1),
          ), // 极轻微阴影 shadow-sm
        ],
      ),
      child: Obx(() {
        return Column(
          children: [
            // 环形进度区域
            SizedBox(
              height: 110,
              child: GridView.count(
                crossAxisCount: 2,
                // 两等宽列
                shrinkWrap: true,
                // 自适应内容高度
                physics: const NeverScrollableScrollPhysics(),
                // 禁止滚动
                crossAxisSpacing: 12.0,
                // 列间距12px (gap-3)
                mainAxisSpacing: 0,
                // 行间距0
                childAspectRatio: 1.6,
                // 宽高比1:1
                children: [
                  // 左侧Micro习惯统计
                  _buildProgressStats(
                    '微习惯',
                    controller.todayStatisticData.value.smallHabit!.per!,
                    '${controller.todayStatisticData.value.smallHabit!.doneCount}/${controller.todayStatisticData.value.smallHabit!.allCount}',
                    _getProgressColor(
                        controller.todayStatisticData.value.smallHabit!.per!),
                  ),
                  // 右侧Regular习惯统计
                  _buildProgressStats(
                    '普通习惯',
                    controller.todayStatisticData.value.normalHabit!.per!,
                    '${controller.todayStatisticData.value.normalHabit!.doneCount}/${controller.todayStatisticData.value.normalHabit!.allCount}',
                    _getProgressColor(
                        controller.todayStatisticData.value.normalHabit!.per!),
                  ),
                ],
              ),
            ),

            // 水平分隔线
            Divider(
              color: const Color(0xFFE9ECEF),
              height: 1,
              thickness: 1,
            ),

            // 底部周趋势区域
            Container(
              width: double.infinity,
              margin: const EdgeInsets.only(top: 4.0, bottom: 0),
              padding: const EdgeInsets.only(top: 8.0),
              child: Column(
                children: [
                  // 趋势容器
                  Center(
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          vertical: 4, horizontal: 8),
                      child: _buildWeeklyProgressGrid(),
                    ),
                  ),
                ],
              ),
            ),
          ],
        );
      }),
    );
  }

  // 周趋势网格
  Widget _buildWeeklyProgressGrid() {
    return Obx(() {
      return Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: controller.todayStatisticData.value.weekData!.map((data) {
          final bool isToday = data.isToday!;
          final String day = data.day!;
          var progress = data.progress!;
          if (isToday) {
            final data = controller.todayStatisticData.value;
            final totalDoneCount =
                data.smallHabit!.doneCount! + data.normalHabit!.doneCount!;
            final totalAllCount =
                data.smallHabit!.allCount! + data.normalHabit!.allCount!;

            // 防止除以0，并返回四舍五入的整数百分比
            progress =
                "${totalAllCount > 0 ? (totalDoneCount / totalAllCount * 100).round() : 0}%";
          }

          return Column(
            children: [
              // 进度百分比
              Text(
                progress,
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: isToday ? AppColors.primary : const Color(0xFF6C757D),
                ),
              ),

              // 圆点指示器
              Container(
                width: 7,
                height: 7,
                margin: const EdgeInsets.symmetric(vertical: 4),
                decoration: BoxDecoration(
                  color: isToday ? AppColors.primary : const Color(0xFFE9ECEF),
                  shape: BoxShape.circle,
                ),
              ),

              // 星期标签
              Text(
                day.tr,
                style: TextStyle(
                  fontSize: 10.5,
                  fontWeight: FontWeight.w500,
                  color: isToday
                      ? const Color(0xFF1F2937)
                      : const Color(0xFF6C757D),
                ),
              ),
            ],
          );
        }).toList(),
      );
    });
  }

  // 根据进度百分比获取颜色
  Color _getProgressColor(int percentage) {
    if (percentage < 50) {
      return Colors.orange; // 较低进度使用橙色
    } else if (percentage < 75) {
      return AppColors.primary; // 中等进度使用标准品牌色
    } else {
      return AppColors.primary; // 高进度使用标准品牌色
    }
  }

  // 进度统计组件
  Widget _buildProgressStats(
      String label, int percentage, String fraction, Color color) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // 环形进度指示器
        SizedBox(
          width: ScreenAdapter.width(190),
          height: ScreenAdapter.height(190),
          child: DashedCircularProgressBar.aspectRatio(
            aspectRatio: 1,
            // width ÷ height
            valueNotifier: controller.smallHabitValueNotifier,
            progress: percentage.toDouble(),
            startAngle: 225,
            sweepAngle: 270,
            foregroundColor: color,
            backgroundColor: Color(0xFFE2F4EC),
            foregroundStrokeWidth: 6,
            backgroundStrokeWidth: 5,
            animation: true,
            // 禁用动画以避免构建时的状态更新
            seekSize: 0,
            child: Center(
              child: Builder(builder: (context) {
                // 使用Builder避免在构建过程中更新状态
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  // 在帧绘制完成后更新值
                  controller.smallHabitValueNotifier.value = 20;
                });
                return Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      '${percentage}%',
                      style: TextStyle(
                        color: Colors.black87,
                        fontWeight: FontWeight.w600,
                        fontSize: ScreenAdapter.fontSize(42),
                      ),
                    ),
                    // 次标签(完成数/总数)
                    Text(
                      fraction,
                      style: const TextStyle(
                        fontSize: 14, // text-xs
                        color: Color(0xFF6C757D), // text-muted-foreground (灰色)
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                );
              }),
            ),
          ),
        ),
        // 标签文本区
        Column(
          children: [
            // 主标签
            Text(
              label.tr,
              style: const TextStyle(
                fontSize: 14, // text-xs
                fontWeight: FontWeight.w500, // font-medium
                color: Color(0xFF1F2937), // 默认文本色(深灰色)
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildFilterTabs() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 10.0),
      child: Obx(() => CustomFilterTabs(
            tabs: const [labelAll, labelUndo, labelDone],
            selectedTab: controller.activeTab.value,
            onTabSelected: (tab) {
              controller.setActiveTab(tab);
            },
          )),
    );
  }

  // Top Habits区域
  Widget _buildTopHabitsSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 保留原有标题栏
        Padding(
          padding: const EdgeInsets.only(bottom: 8.0), // mb-2
          child: Row(
            children: [
              Icon(
                Icons.trending_up,
                size: 16, // h-4 w-4
                color: Colors.green[500], // text-green-500
              ),
              const SizedBox(width: 4), // mr-1
              Text(
                '建议提高难度的习惯'.tr,
                style: TextStyle(
                  fontSize: 15, // text-sm
                  fontWeight: FontWeight.w500, // font-medium
                  color: Color(0xFF4B5563), // 深灰色(#4B5563)
                ),
              ),
            ],
          ),
        ),
        Column(
          children: controller.topHabits.map((v) {
            if (v.habitType == habitTypeSmall) {
              return Column(
                children: [
                  _buildMicroHabitItem(
                    context,
                    v,
                    isStarred: v.isNecessary!,
                  ),
                  SizedBox(
                    height: 12,
                  )
                ],
              );
            }
            return Column(
              children: [
                _buildRegularHabitItem(
                  context,
                  v,
                  isStarred: v.isNecessary!,
                ),
                SizedBox(
                  height: 12,
                )
              ],
            );
          }).toList(),
        ),
        const SizedBox(height: 12), // mb-6
      ],
    );
  }

  // Low Habits区域
  Widget _buildLowHabitsSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 保留原有标题栏
        Padding(
          padding: const EdgeInsets.only(bottom: 8.0), // mb-2
          child: Row(
            children: [
              Icon(
                Icons.trending_down,
                size: 16, // h-4 w-4
                color: Colors.red[500], // text-red-500
              ),
              const SizedBox(width: 4), // mr-1
              Text(
                '建议降低难度的习惯'.tr,
                style: TextStyle(
                  fontSize: 15, // text-sm
                  fontWeight: FontWeight.w500, // font-medium
                  color: Color(0xFF4B5563), // 深灰色(#4B5563)
                ),
              ),
            ],
          ),
        ),
        Column(
          children: controller.lowHabits.map((v) {
            if (v.habitType == habitTypeSmall) {
              return Column(
                children: [
                  _buildMicroHabitItem(
                    context,
                    v,
                    isStarred: v.isNecessary!,
                  ),
                  SizedBox(
                    height: 12,
                  )
                ],
              );
            }
            return Column(
              children: [
                _buildRegularHabitItem(
                  context,
                  v,
                  isStarred: v.isNecessary!,
                ),
                SizedBox(
                  height: 12,
                )
              ],
            );
          }).toList(),
        ),
        const SizedBox(height: 12), // mb-6
      ],
    );
  }

  // Other Habits区域
  Widget _buildOtherHabitsSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 保留原有标题栏
        Padding(
          padding: const EdgeInsets.only(bottom: 8.0), // mb-2
          child: Row(
            children: [
              Icon(
                CustomFonts.otherHabit,
                size: 16, // h-4 w-4
                color: Color(0xFF3482EE), // text-blue-500
              ),
              const SizedBox(width: 4), // mr-1
              Text(
                '进展顺利的习惯'.tr,
                style: TextStyle(
                  fontSize: 15, // text-sm
                  fontWeight: FontWeight.w500, // font-medium
                  color: Color(0xFF4B5563), // 深灰色(#4B5563)
                ),
              ),
            ],
          ),
        ),
        // 使用Obx观察controller中的数据
        Obx(() {
          if (controller.loadingController.isLoading.value) {
            return _buildLoadingContent();
          }
          // 判断是否有Other Habits数据
          if (controller.otherHabits.isEmpty) {
            return _buildEmptyStateContent(
              icon: CustomFonts.otherHabit,
              text: '太棒啦！所有习惯都已完成',
            );
          }

          // 如果有数据，显示原有内容
          return Column(
            children: controller.otherHabits.map((v) {
              if (v.habitType == habitTypeSmall) {
                return Column(
                  children: [
                    _buildMicroHabitItem(
                      context,
                      v,
                      isStarred: v.isNecessary!,
                    ),
                    SizedBox(
                      height: 12,
                    )
                  ],
                );
              }
              return Column(
                children: [
                  _buildRegularHabitItem(
                    context,
                    v,
                    isStarred: v.isNecessary!,
                  ),
                  SizedBox(
                    height: 12,
                  )
                ],
              );
            }).toList(),
          );
        }),
      ],
    );
  }

  // 空状态内容区域（只包含图标和文本，不包括卡片和标题）
  Widget _buildEmptyStateContent({
    required IconData icon,
    required String text,
  }) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Color(0xFFF7F9FA),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(
          color: const Color(0xFFEEEEEE),
          width: 1,
        ),
      ),
      padding: const EdgeInsets.symmetric(vertical: 32, horizontal: 24),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 30,
            color: const Color(0xFF666666), // 灰色图标
          ),
          const SizedBox(height: 16),
          Text(
            text.tr,
            textAlign: TextAlign.center,
            style: const TextStyle(
              fontSize: 16,
              color: Color(0xFF777777), // 中灰色
              fontWeight: FontWeight.normal,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingContent() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Color(0xFFF7F9FA),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(
          color: const Color(0xFFEEEEEE),
          width: 1,
        ),
      ),
      padding: const EdgeInsets.symmetric(vertical: 32, horizontal: 24),
      child: Center(child: CircularProgressIndicator()),
    );
  }

  // 常规习惯卡片 - 简化后
  Widget _buildRegularHabitItem(
      BuildContext context,
    HabitItem item, {
    bool isStarred = false,
  }) {
    final String habitId = item.id!.toString();
    final String progress = '${item.doneCount}/${item.allCount}';
    final String title = item.name ?? "";
    final bool isCompleted = item.doneCount! >= item.allCount!;

    return Obx(() {
      // 计算计时器相关状态
      final hasTimer = controller.timerDurations.containsKey(habitId);
      final isTimerActive = controller.timerActive[habitId] ?? false;
      final timerDuration = controller.formatTimerDuration(habitId);
      double progressValue = 0;
      if (item.allCount != null &&
          item.allCount! > 0 &&
          item.doneCount != null) {
        progressValue = (item.doneCount! / item.allCount!).clamp(0.0, 1.0);
      }
      var isShowEndDate = false;
      var remindDays = 0;
      if (item.endDate != null && item.endDate!.isNotEmpty) {
        remindDays = getDaysFromDateToNow(item.endDate!);
        isShowEndDate = remindDays > 0 ? true : false;
      }

      // 卡片内容部分
      Widget cardContent = GestureDetector(
        onTap: () {
          Map<String, dynamic> args = {
            "userHabitID": item.id!,
            "isShowWeek": item.punchCycleType! != habitPunchCycleMonth,
            "habitCreatedAt": item.createdAt,
            "habitName": item.name,
            "isCompleted": progressValue >= 1,
          };
          // 移除之前可能存在的隐私检查数据
          GetStorage().remove(HiConstants.checkPrivacy);
          if (item.isSetPrivacy!) {
            GetStorage().write(HiConstants.checkPrivacy, args);
          }
          Get.toNamed(Routes.HABIT_DETAIL, arguments: args);
        },
        child: Container(
          decoration: BoxDecoration(
            color: isCompleted ? const Color(0xFFEFF6F4) : Colors.white,
            border: Border.all(
              color: isCompleted
                  ? AppColors.primary.withOpacity(0.3)
                  : const Color(0xFFE9ECEF),
            ),
            borderRadius: BorderRadius.circular(6.0),
          ),
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.0, vertical: 10),
            child: Column(
              children: [
                // 主要内容行
                Row(
                  children: [
                    // 左侧进度环
                    Stack(
                      alignment: Alignment.center, // 确保堆栈中的子组件居中对齐
                      clipBehavior: Clip.none,
                      children: [
                        // 圆形进度指示器
                        SizedBox(
                          width: 29,
                          height: 29,
                          child: CircularProgressIndicator(
                            value: progressValue, // 进度值：0.0-1.0之间
                            strokeWidth: 3,
                            backgroundColor: AppColors.primary.withOpacity(0.1),
                            valueColor: AlwaysStoppedAnimation<Color>(
                              AppColors.primary,
                            ),
                          ),
                        ),

                        // 进度文本或完成图标 - 使用Positioned确保精确定位在中心
                        if (isCompleted)
                          Positioned.fill(
                            child: Container(
                              alignment: Alignment.center,
                              child: Icon(
                                CustomFonts.done,
                                size: 16, // h-4 w-4
                                color: AppColors.primary,
                              ),
                            ),
                          ),

                        // 星标徽章
                        if (isStarred)
                          Positioned(
                            top: -4,
                            right: -4,
                            child: Container(
                              width: 16, // h-4 w-4
                              height: 16,
                              decoration: BoxDecoration(
                                color: Colors.amber[400], // bg-amber-400
                                shape: BoxShape.circle,
                                border: Border.all(color: Colors.white),
                              ),
                              child: const Icon(
                                CustomFonts.star,
                                size: 12,
                                color: Colors.white,
                              ),
                            ),
                          ),
                      ],
                    ),
                    const SizedBox(width: 12),

                    // 中间内容区
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // 标题
                          Text(
                            title,
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                              fontSize: 16,
                              color: Color(0xFF1F2937),
                            ),
                            maxLines: 2,
                          ),

                          // 进度文本行
                          Row(
                            children: [
                              Text(
                                progress,
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey[600],
                                ),
                              ),

                              // 剩余天数
                              if (isShowEndDate) ...[
                                const SizedBox(width: 8),
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 6, vertical: 2),
                                  decoration: BoxDecoration(
                                    color: const Color(0xFFFFF7ED),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Text(
                                    '${"剩余 ".tr}$remindDays${" 天".tr}',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Color(0xFFF97316),
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                              ],

                              // 计时器徽章（如果有）
                              if (hasTimer && isTimerActive) ...[
                                SizedBox(width: 8),
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 6, vertical: 2),
                                  decoration: BoxDecoration(
                                    color: AppColors.error,
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Text(
                                    timerDuration,
                                    style: TextStyle(
                                      fontFamily: 'RobotoMono',
                                      fontSize: 11,
                                      fontWeight: FontWeight.w500,
                                      color: Colors.white,
                                    ),
                                  ),
                                ),
                              ],
                            ],
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(width: 12), // ml-3
                    // 右侧操作区
                    GestureDetector(
                      onTap: () async {
                        if (isTimerActive) {
                          // 暂停计时
                          await controller.toggleTimer(habitId);

                          _buildShowTimerDialog(habitId, habitId);
                        } else {
                          await controller.punchHabit(item.id!);
                        }
                      },
                      child: // 完成/暂停按钮
                          Container(
                        width: 32,
                        height: 32,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(6),
                          color: isCompleted || isTimerActive
                              ? isTimerActive
                                  ? AppColors.error
                                  : AppColors.primary
                              : Colors.white,
                          shape: BoxShape.rectangle,
                          border: isCompleted || isTimerActive
                              ? null
                              : Border.all(color: Colors.grey[300]!),
                        ),
                        child: Icon(
                          isTimerActive
                              ? CustomFonts.homeStop
                              : CustomFonts.done,
                          size: 12,
                          color: isCompleted || isTimerActive
                              ? Colors.white
                              : Colors.grey[600],
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      );

      // 如果不需要滑动操作或计时器激活，直接返回卡片内容
      if (isTimerActive) {
        return cardContent;
      }

      // 添加滑动功能
      return Slidable(
        key: Key('slidable_habit_$habitId'),
        endActionPane: ActionPane(
          motion: const BehindMotion(),
          extentRatio: 0.42,
          children: [
            // 计时按钮
            SlidableAction(
              onPressed: (context) {
                // 如果有其他活动计时器
                if (controller.activeTimerId.isNotEmpty &&
                    controller.activeTimerId.value != habitId &&
                    controller.timerActive[controller.activeTimerId.value] ==
                        true) {
                  showErrorMessage(
                      HiConstants.errorTypeWarning, "当前仅允许一个任务进行计时".tr);
                  return;
                }
                controller.toggleTimer(habitId);
                // 保存计时结果
                controller.reckonUserHabit(
                  item.id!,
                  isEnd: false,
                );
              },
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              icon: CustomFonts.rollbackTime,
              padding: EdgeInsets.only(left: 10),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(6),
                bottomLeft: Radius.circular(6),
              ),
            ),
            // 笔记按钮
            SlidableAction(
              onPressed: (_) =>
                  controller.openNoteDialog(context, int.parse(habitId), title),
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              icon: CustomFonts.note,
              padding: EdgeInsets.only(right: 10),
            ),
            // 取消打卡按钮
            SlidableAction(
              onPressed: (context) async {
                if (!item.isAllowCancelPunch!) {
                  showErrorMessage(HiConstants.errorTypeWarning, "不允许取消打卡".tr);
                }
                await controller.cancelPunchHabit(int.parse(habitId));
              },
              backgroundColor: Color(0xFFF97316),
              foregroundColor: Colors.white,
              icon: CustomFonts.cancel,
              padding: EdgeInsets.only(right: 5),
              borderRadius: const BorderRadius.only(
                topRight: Radius.circular(6),
                bottomRight: Radius.circular(6),
              ),
            ),
          ],
        ),
        child: cardContent,
      );
    });
  }

  // 微习惯卡片
  Widget _buildMicroHabitItem(BuildContext context, HabitItem item, {bool isStarred = false}) {
    final String habitId = item.id!.toString();
    final String title = item.name!;
    final String progress = '${item.doneCount}/${item.allCount}';
    final bool isCompleted = item.doneCount! >= item.allCount!;

    // 子阶段ID
    final String stage1Key = "${item.id}-${item.smallStages![0].stageId!}";
    final String stage2Key = "${item.id}-${item.smallStages![1].stageId!}";

    return Obx(() {
      // 检查子阶段计时状态
      final stage1TimerActive = controller.timerActive[stage1Key] ?? false;
      final stage2TimerActive = controller.timerActive[stage2Key] ?? false;
      final isExpanded = controller.expandedHabits.contains(habitId);

      // 获取当前活动的子阶段计时信息
      String activeStageTimerDuration = '';
      bool hasStageTimer = false;

      if (stage1TimerActive) {
        activeStageTimerDuration = controller.formatTimerDuration(stage1Key);
        hasStageTimer = true;
      } else if (stage2TimerActive) {
        activeStageTimerDuration = controller.formatTimerDuration(stage2Key);
        hasStageTimer = true;
      }

      var isShowEndDate = false;
      var remindDays = 0;
      if (item.endDate != null && item.endDate!.isNotEmpty) {
        remindDays = getDaysFromDateToNow(item.endDate!);
        isShowEndDate = remindDays > 0 ? true : false;
      }

      double progressValue = 0;
      if (item.allCount != null &&
          item.allCount! > 0 &&
          item.doneCount != null) {
        progressValue = (item.doneCount! / item.allCount!).clamp(0.0, 1.0);
      }

      // 微习惯卡片本身不可滑动
      return GestureDetector(
        onTap: () {
          Map<String, dynamic> args = {
            "userHabitID": item.id!,
            "isShowWeek": item.punchCycleType! != habitPunchCycleMonth,
            "habitCreatedAt": item.createdAt,
            "habitName": item.name,
            "isCompleted": isCompleted,
          };
          // 移除之前可能存在的隐私检查数据
          GetStorage().remove(HiConstants.checkPrivacy);
          if (item.isSetPrivacy!) {
            GetStorage().write(HiConstants.checkPrivacy, args);
          }
          Get.toNamed(Routes.HABIT_DETAIL, arguments: args);
        },
        child: Container(
          decoration: BoxDecoration(
            color: isCompleted ? Color(0xFFEFF6F4) : Colors.white,
            border: Border.all(
              color: isCompleted
                  ? AppColors.primary.withOpacity(0.3)
                  : const Color(0xFFE9ECEF),
            ),
            borderRadius: BorderRadius.circular(8.0), // rounded-lg
            boxShadow: isExpanded
                ? [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      blurRadius: 2,
                      offset: const Offset(0, 1),
                    ),
                  ]
                : null,
          ),
          child: Column(
            children: [
              // 主卡片内容
              Padding(
                padding: const EdgeInsets.symmetric(
                    horizontal: 16.0, vertical: 10.0),
                child: Row(
                  children: [
                    // 左侧进度环
                    Stack(
                      alignment: Alignment.center, // 确保堆栈中的子组件居中对齐
                      clipBehavior: Clip.none,
                      children: [
                        // 圆形进度指示器
                        SizedBox(
                          width: 29,
                          height: 29,
                          child: CircularProgressIndicator(
                            value: progressValue, // 进度值：0.0-1.0之间
                            strokeWidth: 3,
                            backgroundColor: AppColors.primary.withOpacity(0.1),
                            valueColor: AlwaysStoppedAnimation<Color>(
                              AppColors.primary,
                            ),
                          ),
                        ),

                        // 进度文本或完成图标 - 使用Positioned确保精确定位在中心
                        if (isCompleted)
                          Positioned.fill(
                            child: Container(
                              alignment: Alignment.center,
                              child: Icon(
                                CustomFonts.done,
                                size: 16, // h-4 w-4
                                color: AppColors.primary,
                              ),
                            ),
                          ),

                        // 星标徽章
                        if (isStarred)
                          Positioned(
                            top: -4,
                            right: -4,
                            child: Container(
                              width: 16, // h-4 w-4
                              height: 16,
                              decoration: BoxDecoration(
                                color: Colors.amber[400], // bg-amber-400
                                shape: BoxShape.circle,
                                border: Border.all(color: Colors.white),
                              ),
                              child: const Icon(
                                CustomFonts.star,
                                size: 12,
                                color: Colors.white,
                              ),
                            ),
                          ),
                      ],
                    ),
                    const SizedBox(width: 12), // ml-3
                    // 中间内容区
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // 标题行
                          Row(
                            children: [
                              // 标题
                              Expanded(
                                child: Text(
                                  title,
                                  style: const TextStyle(
                                    fontWeight: FontWeight.w500,
                                    fontSize: 16, // text-base
                                    color: Color(0xFF1F2937),
                                  ),
                                  maxLines: 2,
                                ),
                              ),
                            ],
                          ),
                          // 进度文本
                          Row(
                            children: [
                              Text(
                                progress,
                                style: TextStyle(
                                  fontSize: 12, // text-xs
                                  color:
                                      Colors.grey[600], // text-muted-foreground
                                ),
                              ),

                              // 剩余天数
                              if (isShowEndDate) ...[
                                const SizedBox(width: 8),
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 6, vertical: 2),
                                  decoration: BoxDecoration(
                                    color: const Color(0xFFFFF7ED),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Text(
                                    '${"剩余 ".tr}$remindDays${" 天".tr}',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Color(0xFFF97316),
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                              ],

                              SizedBox(width: 8),
                              // 显示主卡片计时器徽章
                              if (hasStageTimer) ...[
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 6, vertical: 2),
                                  decoration: BoxDecoration(
                                    color: AppColors.error,
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Text(
                                    activeStageTimerDuration,
                                    style: TextStyle(
                                      fontFamily: 'RobotoMono',
                                      fontSize: 11,
                                      fontWeight: FontWeight.w500,
                                      color: Colors.white,
                                    ),
                                  ),
                                ),
                              ]
                            ],
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(width: 12), // ml-3
                    // 右侧操作区
                    InkWell(
                      onTap: () {
                        controller.toggleHabitExpanded(habitId);
                      },
                      child: SizedBox(
                        width: 32,
                        height: 32,
                        child: Transform.rotate(
                          angle: controller.expandedHabits.contains(habitId)
                              ? 3.14159
                              : 0, // 180度旋转
                          child: Icon(
                            CustomFonts.homeDown,
                            size: 13,
                            color: Colors.grey[600],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              // 展开的内容
              if (controller.expandedHabits.contains(habitId))
                Container(
                  width: double.infinity,
                  decoration: const BoxDecoration(
                    border: Border(
                      top: BorderSide(color: Color(0xFFE9ECEF)),
                    ),
                  ),
                  padding: const EdgeInsets.fromLTRB(16.0, 4.0, 16.0, 16.0),
                  // px-4 pb-4 pt-1
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 双阶段标题
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8.0),
                        child: Row(
                          children: [
                            Icon(
                              CustomFonts.star,
                              size: 14, // h-3 w-3
                              color: Colors.amber[500], // text-amber-500
                            ),
                            const SizedBox(width: 4),
                            Text(
                              '双阶段习惯'.tr,
                              style: TextStyle(
                                fontSize: 13, // text-xs
                                color:
                                    Colors.grey[600], // text-muted-foreground
                              ),
                            ),
                          ],
                        ),
                      ),
                      // 阶段项目
                      const SizedBox(height: 8), // space-y-2
                      _buildHabitStageItem(context, item.smallStages![0], item.allCount!,
                          habitId, item.isAllowCancelPunch!),
                      const SizedBox(height: 8),
                      _buildHabitStageItem(context, item.smallStages![1], item.allCount!,
                          habitId, item.isAllowCancelPunch!),
                    ],
                  ),
                ),
            ],
          ),
        ),
      );
    });
  }

  // 习惯阶段项目
  Widget _buildHabitStageItem(BuildContext context, SmallStageItem smallStage, int targetCount,
      String habitID, bool isAllowCancelPunch) {
    final String smallStageId = smallStage.stageId.toString();
    final bool isCompleted = smallStage.punchedCount! >= targetCount;
    final String smallStageKey = '$habitID-$smallStageId';

    return Obx(() {
      // 计算计时器相关状态
      final hasTimer = controller.timerDurations.containsKey(smallStageKey);
      final isTimerActive = controller.timerActive[smallStageKey] ?? false;
      final timerDuration = controller.formatTimerDuration(smallStageKey);
      double progressValue = 0;
      if (smallStage.punchedCount != null && targetCount > 0) {
        progressValue =
            (smallStage.punchedCount! / targetCount).clamp(0.0, 1.0);
      }

      // 如果计时器激活，则不显示左滑效果
      if (isTimerActive) {
        return _buildStageHabitCard(
            smallStage.name!,
            isCompleted,
            hasTimer,
            isTimerActive,
            timerDuration,
            smallStageId,
            habitID,
            progressValue,
            smallStage.punchedCount!);
      }

      return Slidable(
        key: Key('slidable_stage_$smallStageKey'),
        endActionPane: ActionPane(
          motion: const BehindMotion(),
          extentRatio: 0.42, // 控制滑出区域宽度，适合两个按钮
          children: [
            // 计时按钮
            SlidableAction(
              onPressed: (context) {
                // 如果有其他活动计时器
                if (controller.activeTimerId.isNotEmpty &&
                    controller.activeTimerId.value != smallStageKey &&
                    controller.timerActive[controller.activeTimerId.value] ==
                        true) {
                  showErrorMessage(
                      HiConstants.errorTypeWarning, "当前仅允许一个任务进行计时".tr);
                  return;
                }
                controller.toggleTimer(smallStageKey);
                // 保存计时结果
                controller.reckonUserHabit(
                  int.parse(habitID),
                  isEnd: false,
                  smallStageID: smallStage.stageId!,
                );
              },
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              icon: CustomFonts.rollbackTime,
              padding: EdgeInsets.only(left: 10),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(6),
                bottomLeft: Radius.circular(6),
              ),
            ),
            // 笔记按钮
            SlidableAction(
              onPressed: (_) => controller.openNoteDialog(context,
                  int.parse(habitID), smallStage.name,
                  smallStageID: int.parse(smallStageId)),
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              icon: CustomFonts.note,
              padding: EdgeInsets.only(right: 10),
            ),
            // 取消打卡按钮
            SlidableAction(
              onPressed: (context) async {
                if (!isAllowCancelPunch) {
                  showErrorMessage(HiConstants.errorTypeWarning, "不允许取消打卡".tr);
                  return;
                }
                if (smallStage.punchedCount! > 0) {
                  await controller.cancelPunchHabit(int.parse(habitID),
                      smallStageID: int.parse(smallStageId));
                }
              },
              backgroundColor: Color(0xFFF97316),
              foregroundColor: Colors.white,
              icon: CustomFonts.cancel,
              padding: EdgeInsets.only(right: 5),
              borderRadius: const BorderRadius.only(
                topRight: Radius.circular(6),
                bottomRight: Radius.circular(6),
              ),
            ),
          ],
        ),
        child: _buildStageHabitCard(
            smallStage.name!,
            isCompleted,
            hasTimer,
            isTimerActive,
            timerDuration,
            smallStageId,
            habitID,
            progressValue,
            smallStage.punchedCount!),
      );
    });
  }

  // 阶段习惯卡片（计时器激活时使用，不可滑动）
  Widget _buildStageHabitCard(
    String title,
    bool isCompleted,
    bool hasTimer,
    bool isTimerActive,
    String timerDuration,
    String stageID,
    String habitID,
    double progressValue,
    int doneCount,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: doneCount > 0
            ? AppColors.primary.withOpacity(0.05) // bg-primary/5
            : Colors.white, // bg-background
        border: Border.all(
          color: doneCount > 0
              ? AppColors.primary.withOpacity(0.3) // border-primary/30
              : const Color(0xFFE9ECEF), // border-border
        ),
        borderRadius: BorderRadius.circular(6.0), // rounded-md
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 10.0),
        // px-3 py-2.5
        child: Row(
          children: [
            // 阶段名称
            Expanded(
              child: Text(
                title,
                style: const TextStyle(
                  fontWeight: FontWeight.w500,
                  fontSize: 14, // text-sm
                  color: Color(0xFF1F2937),
                ),
              ),
            ),
            // 右侧操作区
            GestureDetector(
              onTap: () async {
                var stageKey = "$habitID-$stageID";
                if (isTimerActive) {
                  // 暂停计时
                  await controller.toggleTimer(stageKey);

                  _buildShowTimerDialog(habitID, stageKey, stageID: stageID);
                } else {
                  await controller.punchHabit(int.parse(habitID),
                      smallStageID: int.parse(stageID));
                }
              },
              child: Container(
                width: 24, // h-6 w-6
                height: 24,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(6),
                  color: doneCount > 0 || isTimerActive
                      ? isTimerActive
                          ? AppColors.error
                          : AppColors.primary
                      : Colors.white,
                  shape: BoxShape.rectangle,
                  border: doneCount > 0 || isTimerActive
                      ? null
                      : Border.all(color: Colors.grey[300]!),
                ),
                child: isTimerActive
                    ? Icon(
                        CustomFonts.homeStop,
                        size: 10,
                        color: Colors.white,
                      )
                    : doneCount > 0
                        ? Center(
                            child: Text(
                              "$doneCount",
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.white,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          )
                        : Icon(
                            CustomFonts.done,
                            size: 10, // h-3 w-3
                            color: Colors.grey[600],
                          ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _buildShowTimerDialog(String habitID, String timerKey,
      {String stageID = "0"}) {
    // 显示确认对话框
    showDialog(
      context: Get.context!,
      barrierDismissible: true, // 允许点击外部关闭对话框
      barrierColor: Colors.black54,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          title: Row(
            children: [
              Icon(Icons.timer, color: AppColors.primary, size: 24),
              const SizedBox(width: 8),
              Text(
                '计时停止'.tr,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              // 添加关闭按钮
              InkWell(
                onTap: () {
                  // 关闭对话框并继续计时
                  controller.toggleTimer(timerKey);
                  Navigator.of(context).pop();
                },
                borderRadius: BorderRadius.circular(12),
                child: Container(
                  padding: const EdgeInsets.all(4),
                  child: Icon(
                    Icons.close,
                    color: Colors.grey[600],
                    size: 20,
                  ),
                ),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '${"时长记录".tr}: ${controller.formatTimerDuration(timerKey)}',
                style: TextStyle(
                  fontSize: 15,
                  color: Colors.grey[700],
                ),
              ),
              const SizedBox(height: 12),
              Text(
                '是否保存此次计时？'.tr,
                style: TextStyle(fontSize: 15),
              ),
            ],
          ),
          actions: [
            TextButton(
              child: Text(
                '放弃'.tr,
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 15,
                  fontWeight: FontWeight.w500,
                ),
              ),
              onPressed: () {
                // 取消计时
                controller.cancelReckonUserHabit(
                  int.parse(habitID),
                  smallStageID: int.parse(stageID),
                );
                controller.resetTimer(timerKey);
                controller.refreshHabitData();
                Navigator.of(context).pop();
              },
            ),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              ),
              child: Text(
                '保存'.tr,
                style: TextStyle(
                  fontSize: 15,
                  fontWeight: FontWeight.w500,
                ),
              ),
              onPressed: () async {
                // 获取计时时长（秒）
                final timerDuration =
                    controller.timerDurations[timerKey]?.inSeconds ?? 0;

                // 保存计时结果
                await controller.reckonUserHabit(
                  int.parse(habitID),
                  duration: timerDuration,
                  isEnd: true,
                  smallStageID: int.parse(stageID),
                );

                // 重置计时器
                controller.resetTimer(timerKey);
                controller.refreshHabitData();
                Navigator.of(context).pop();
              },
            ),
          ],
          actionsPadding:
              const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        );
      },
    ).then((_) {
      // 检查计时器状态，如果弹窗被关闭但未通过按钮操作（如点击外部区域或返回键）
      // 则恢复计时
      if (controller.timerActive[timerKey] == false &&
          controller.timerDurations[timerKey] != null &&
          controller.timerDurations[timerKey]!.inSeconds > 0) {
        controller.toggleTimer(timerKey);
      }
    });
  }

  // 构建错误状态页面
  Widget _buildErrorState() {
    return CustomScrollView(
      physics: const AlwaysScrollableScrollPhysics(),
      slivers: [
        // 统计卡片区 - 可滚动
        SliverToBoxAdapter(
          child: _buildStatisticsCard(),
        ),

        // 筛选标签区 - 滚动到顶部时固定
        SliverPersistentHeader(
          pinned: true, // 设置为true以实现吸顶效果
          delegate: _SliverAppBarDelegate(
            minHeight: 60, // 最小高度
            maxHeight: 60, // 最大高度
            child: _buildFilterTabs(),
          ),
        ),

        // 错误状态区域 - 替换习惯列表
        SliverToBoxAdapter(
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 32),
            child: ToRefresh(
              onRefresh: () async {
                // 重新加载习惯数据
                await controller.refreshHabitData();
              },
            ),
          ),
        ),
      ],
    );
  }
}

// SliverPersistentHeader的委托类，用于实现FilterTabs的吸顶效果
class _SliverAppBarDelegate extends SliverPersistentHeaderDelegate {
  _SliverAppBarDelegate({
    required this.minHeight,
    required this.maxHeight,
    required this.child,
  });

  final double minHeight;
  final double maxHeight;
  final Widget child;

  @override
  double get minExtent => minHeight;

  @override
  double get maxExtent => maxHeight;

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return SizedBox.expand(child: child);
  }

  @override
  bool shouldRebuild(_SliverAppBarDelegate oldDelegate) {
    return maxHeight != oldDelegate.maxHeight ||
        minHeight != oldDelegate.minHeight ||
        child != oldDelegate.child;
  }
}
