import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_advanced_switch/flutter_advanced_switch.dart';
import 'package:get/get.dart';
import 'package:life_habit_app/app/theme/app_colors.dart';
import 'package:life_habit_app/app/utils/customFonts.dart';

import '../../../../utils/enums.dart';
import '../../../../utils/hi_constants.dart';
import '../../../../utils/toast.dart';
import '../../../../models/buddy_model.dart';
import '../controllers/habit_add_controller.dart';
import '../../../discovery/views/discovery_view.dart';

class HabitAddView extends GetView<HabitAddController> {
  const HabitAddView({super.key});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Scaffold(
          backgroundColor: Colors.white,
          appBar: _buildAppBar(),
          body: _buildForm(context),
        ),
        // 科学依据弹窗 - 移到 Scaffold 外层
        Obx(() {
          if (controller.showScientificDialog.value &&
              controller.templateData != null) {
            return ScientificInsightDialog(
              templateData: controller.templateData!,
              onClose: controller.hideScientificDialog,
            );
          }
          return const SizedBox.shrink();
        }),
      ],
    );
  }

  // AppBar
  AppBar _buildAppBar() {
    return AppBar(
      backgroundColor: Colors.white,
      elevation: 0,
      centerTitle: true,
      leading: IconButton(
        padding: const EdgeInsets.only(left: 10),
        icon: const Icon(Icons.arrow_back, color: Color(0xFF212121), size: 20),
        onPressed: () => Get.back(),
      ),
      titleSpacing: 6,
      title: Row(
        children: [
          Text(
            '新习惯'.tr,
            style: const TextStyle(
              color: Color(0xFF464646),
              fontSize: 20,
              fontWeight: FontWeight.w500,
            ),
          ),
          const Padding(
            padding: EdgeInsets.only(left: 8),
            child: Icon(CustomFonts.dobuleCheck,
                color: AppColors.primary, size: 18),
          )
        ],
      ),
    );
  }

  // Main Form
  Widget _buildForm(BuildContext context) {
    final fullLocale = Get.locale?.toString() ?? '';
    var length = fullLocale.startsWith('zh') ? 20 : 40;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildBasicInfoCard(context, length),
          _buildStagesCard(),
          _buildRecordCard(),
          _buildScheduleCard(),
          _buildReminderCard(context),
          _buildAdvancedOptionsToggle(context),
          _buildAdvancedOptionsCard(),

          // 添加淡入动画，让错误信息出现更自然
          Obx(() => AnimatedSwitcher(
                duration: const Duration(milliseconds: 300),
                child: _buildErrorMessages(),
              )),

          _buildBottomButtons(),
        ],
      ),
    );
  }



  // 获取自适应卡片间距
  double _getAdaptiveCardSpacing(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    if (screenHeight > 800) return 22.0;  // 大屏幕
    if (screenHeight > 600) return 18.0;  // 中等屏幕
    return 16.0;  // 小屏幕
  }

  // Error Messages Section
  Widget _buildErrorMessages() {
    if (controller.errorMessages.isEmpty) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.only(bottom: 16, top: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.red.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.red.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.error_outline, color: Colors.red.shade600, size: 16),
              const SizedBox(width: 8),
              Text(
                '请修复以下错误：'.tr,
                style: TextStyle(
                  color: Colors.red.shade600,
                  fontWeight: FontWeight.w500,
                  fontSize: 14,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          ...controller.errorMessages.map(
            (error) => Padding(
              padding: const EdgeInsets.only(left: 24, bottom: 4),
              child: Text(
                '• $error',
                style: TextStyle(
                  color: Colors.red.shade700,
                  fontSize: 13,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Basic Info Card
  Widget _buildBasicInfoCard(BuildContext context, int maxLength) {
    return _buildCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 习惯名称区域
          Text(
            '习惯名称'.tr,
            style: const TextStyle(
              fontSize: 17,
              fontWeight: FontWeight.w500,
              color: Color(0xFF464646),
            ),
          ),
          const SizedBox(height: 12),
          _buildTextField(
            controller: controller.habitNameController,
            hintText: '晨跑、阅读',
            inputFormatters: [LengthLimitingTextInputFormatter(maxLength)],
          ),

          // 视觉分隔
          Container(
            margin: const EdgeInsets.symmetric(vertical: 20),
            height: 1,
            color: const Color(0xFFF5F5F5),
          ),

          // 习惯类型区域
          Text(
            '习惯类型'.tr,
            style: const TextStyle(
              fontSize: 17,
              fontWeight: FontWeight.w500,
              color: Color(0xFF464646),
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(child: _buildTypeButton(habitTypeRecord, '记录')),
              const SizedBox(width: 12),
              Expanded(child: _buildTypeButton(habitTypeNormal, '普通习惯')),
              const SizedBox(width: 12),
              Expanded(
                  child: _buildTypeButton(habitTypeSmall, '微习惯',
                      icon: CustomFonts.mutilStar)),
            ],
          ),
          const SizedBox(height: 8),
        ],
      ),
    );
  }



  // Stages Card - 优化微习惯区域的视觉层次和信息密度
  Widget _buildStagesCard() {
    return Obx(() {
      if (controller.habitType.value != habitTypeSmall) {
        return const SizedBox.shrink();
      }

      return _buildCard(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 入门小步骤区域 - 优化间距和层次
            _buildStageSection(
              title: '入门小步骤'.tr,
              description: '定一个小到不可能失败的第一步'.tr,
              controller: controller.stage1Controller,
              hintText: '做 1 个俯卧撑',
              onChanged: (value) => controller.stage1.value = value,
            ),

            // 优化的分隔区域 - 使用更轻量的分割方式
            Container(
              margin: const EdgeInsets.symmetric(vertical: 16),
              height: 1,
              color: const Color(0xFFF5F5F5),
            ),

            // 理想目标区域 - 保持一致的设计语言
            _buildStageSection(
              title: '理想目标'.tr,
              description: '有信心时可以尝试的完整目标'.tr,
              controller: controller.stage2Controller,
              hintText: '做 10 个俯卧撑',
              onChanged: (value) => controller.stage2.value = value,
            ),
          ],
        ),
      );
    });
  }

  // 微习惯阶段区域构建器 - 统一的设计语言
  Widget _buildStageSection({
    required String title,
    required String description,
    required TextEditingController controller,
    required String hintText,
    required Function(String) onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 标题 - 与页面其他标题保持一致
        Text(
          title,
          style: const TextStyle(
            fontSize: 17, // 与其他区域标题一致
            fontWeight: FontWeight.w500,
            color: Color(0xFF464646),
          ),
        ),
        const SizedBox(height: 6), // 优化间距

        // 描述文案 - 降低视觉权重但保持可读性
        Text(
          description,
          style: const TextStyle(
            fontSize: 13,
            color: Color(0xFF888888), // 稍微降低颜色权重
            height: 1.3, // 优化行高
          ),
        ),
        const SizedBox(height: 12), // 优化间距

        // 输入框 - 保持与其他输入框一致
        _buildTextField(
          controller: controller,
          onChanged: onChanged,
          hintText: hintText,
        ),
      ],
    );
  }

  // 记录类型卡片 - 统一设计语言，优化视觉协调性
  Widget _buildRecordCard() {
    return Obx(() {
      if (controller.habitType.value != habitTypeRecord) {
        return const SizedBox.shrink();
      }

      return _buildCard(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题 - 与其他区域保持一致的字体大小
            Text(
              '跟踪方式'.tr,
              style: const TextStyle(
                fontSize: 17,
                fontWeight: FontWeight.w500,
                color: Color(0xFF464646),
              ),
            ),
            const SizedBox(height: 16), // 稍微增加间距，与其他卡片保持一致
            _buildRecordSelector(),
          ],
        ),
      );
    });
  }

  // Schedule Card
  Widget _buildScheduleCard() {
    return _buildCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 频率选择区域
          _buildFrequencySection(),

          // 视觉分隔
          Container(
            margin: const EdgeInsets.symmetric(vertical: 20),
            height: 1,
            color: const Color(0xFFF5F5F5),
          ),

          // 日期/天数选择区域
          _buildDateSection(),

          // 视觉分隔
          Container(
            margin: const EdgeInsets.symmetric(vertical: 20),
            height: 1,
            color: const Color(0xFFF5F5F5),
          ),

          // 次数设置区域
          _buildTimesSection(),
        ],
      ),
    );
  }

  // 频率选择区域
  Widget _buildFrequencySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '打卡频率'.tr,
          style: const TextStyle(
            fontSize: 17,
            fontWeight: FontWeight.w500,
            color: Color(0xFF464646),
          ),
        ),
        const SizedBox(height: 16),
        _buildCycleSelector(),
      ],
    );
  }

  // 日期选择区域
  Widget _buildDateSection() {
    return Obx(() {
      String scheduleTitle = '选择星期';

      if (controller.cycle.value == habitPunchCycleWeek) {
        scheduleTitle = '每周打卡几天';
      } else if (controller.cycle.value == habitPunchCycleMonth) {
        scheduleTitle = '每月打卡几天';
      }

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            scheduleTitle.tr,
            style: const TextStyle(
              fontSize: 17,
              fontWeight: FontWeight.w500,
              color: Color(0xFF464646),
            ),
          ),
          const SizedBox(height: 16),

          // Schedule content
          if (controller.cycle.value == habitPunchCycleFix)
            _buildDaySelector()
          else
            _buildTargetDaysControl(),
        ],
      );
    });
  }

  // 次数设置区域
  Widget _buildTimesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "每天打卡几次".tr,
          style: const TextStyle(
            fontSize: 17,
            fontWeight: FontWeight.w500,
            color: Color(0xFF464646),
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Text(
              '每日次数'.tr,
              style: const TextStyle(
                fontSize: 15,
                color: Color(0xFF4A5568),
                fontWeight: FontWeight.w500,
              ),
            ),
            const Spacer(),
            _buildCounterControl(),
          ],
        ),
      ],
    );
  }

  // 目标天数控制器
  Widget _buildTargetDaysControl() {
    return Row(
      children: [
        Text(
          '目标天数'.tr,
          style: const TextStyle(
            fontSize: 15,
            color: Color(0xFF4A5568),
            fontWeight: FontWeight.w500,
          ),
        ),
        const Spacer(),
        // Use different controllers to ensure data isolation
        controller.cycle.value == habitPunchCycleWeek
            ? _buildCustomCounterControl(
                increment: controller.incrementWeeklyTimes,
                decrement: controller.decrementWeeklyTimes,
                value: controller.weeklyCheckTimes,
              )
            : _buildCustomCounterControl(
                increment: controller.incrementMonthlyTimes,
                decrement: controller.decrementMonthlyTimes,
                value: controller.monthlyCheckTimes,
              ),
      ],
    );
  }

  Widget _buildReminderCard(BuildContext context) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: const Color(0xFFF0F0F0), width: 1),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.04),
                blurRadius: 4,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 提醒功能
              _buildReminderSection(),
              const SizedBox(height: 20),
              _buildBuddySection(context),
            ],
          ),
        ),
        // 通知权限检查提示
        Obx(() => (!controller.hasNotificationPermission.value && controller.reminderSwitch.value)
            ? Container(
          margin:  EdgeInsets.fromLTRB(16, 8, 16, _getAdaptiveCardSpacing(context)),
          child: _buildNotificationPermissionTip(),
        )
            : SizedBox(height: _getAdaptiveCardSpacing(context))),
      ],
    );
  }

  /// 构建提醒设置区域
  Widget _buildReminderSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 提醒开关行
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                // 更突出的提醒图标设计
                Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: AppColors.primary,
                    borderRadius: BorderRadius.circular(6),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.primary.withOpacity(0.2),
                        blurRadius: 4,
                        offset: Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Icon(
                    Icons.notifications_active_rounded,
                    size: 14,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  '习惯提醒'.tr,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF464646),
                  ),
                ),
              ],
            ),
            // 开关
            Container(
              height: 28,
              width: 50,
              child: Obx(() => AdvancedSwitch(
                enabled: true,
                initialValue: controller.reminderTimes.isNotEmpty,
                controller: controller.reminderController,
                activeColor: AppColors.primary,
                inactiveColor: Colors.grey.shade200,
                onChanged: (value) {
                  if (value) {
                    // 开启提醒 - 显示添加提醒弹窗
                    controller.showAddReminderBottomSheet();
                  } else {
                    // 关闭提醒 - 清空所有提醒
                    controller.reminderSwitch.value = false;
                    controller.reminderController.value = false;
                    controller.reminderTimes.clear();

                  }
                },
              )),
            ),
          ],
        ),

        // 提醒列表
        Obx(() => controller.reminderSwitch.value && controller.reminderTimes.isNotEmpty
            ? Column(
                children: [
                  const SizedBox(height: 10),
                  ...controller.reminderTimes.asMap().entries.map((entry) {
                    final index = entry.key;
                    final timeString = entry.value;
                    return _buildReminderItem(timeString, index);
                  }).toList(),
                ],
              )
            : Container()),

        // 添加提醒按钮
        Obx(() => controller.reminderSwitch.value && controller.canAddMoreReminders()
            ? Column(
                children: [
                  _buildAddReminderButton(),
                ],
              )
            : Container()),
      ],
    );
  }

  /// 构建单个提醒项
  Widget _buildReminderItem(String timeString, int index) {
    return Container(
      margin: EdgeInsets.only(bottom: index == 2 ? 0 : 12),
      child: Row(
        children: [
          // 删除按钮
          GestureDetector(
            onTap: () => controller.removeReminderTime(index),
            child: Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                color: Color(0xFFE86144),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                CustomFonts.removeWithoutBorder,
                size: 12,
                color: Colors.white,
              ),
            ),
          ),
          const SizedBox(width: 12),
          // 提醒时间
          Expanded(
            child: Text(
              timeString,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Color(0xFF333333),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建添加提醒按钮
  Widget _buildAddReminderButton() {
    return GestureDetector(
      onTap: controller.showAddReminderBottomSheet,
      child: Row(
        children: [
          Container(
            width: 20,
            height: 20,
            decoration: BoxDecoration(
              color: AppColors.primary,
              shape: BoxShape.circle,
            ),
            child: const Icon(
              CustomFonts.addWithoutBorder,
              size: 12,
              color: Colors.white,
            ),
          ),
          const SizedBox(width: 12),
          Text(
            '添加提醒时间'.tr,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: AppColors.primary,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建搭子功能区域
  Widget _buildBuddySection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 搭子标题行 - 优化布局减少拥挤感
        Row(
          children: [
            // 更突出的搭子图标设计
            Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Color(0xFFFF6B6B),
                    Color(0xFFFF8E8E),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(6),
                boxShadow: [
                  BoxShadow(
                    color: Color(0xFFFF6B6B).withOpacity(0.3),
                    blurRadius: 4,
                    offset: Offset(0, 2),
                  ),
                ],
              ),
              child: Icon(
                Icons.people_alt_rounded,
                size: 14,
                color: Colors.white,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        '习惯搭子'.tr,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: Color(0xFF464646),
                        ),
                      ),
                      const SizedBox(width: 8),
                      _buildSuccessRateTag(),
                    ],
                  ),
                  const SizedBox(height: 6), // 增加间距，提升呼吸感
                  Text(
                    '邀请好友互相监督，一起养成好习惯'.tr,
                    style: const TextStyle(
                      fontSize: 13,
                      color: Color(0xFF666666),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),

        // 已选择的搭子列表
        Obx(() => controller.selectedBuddies.isNotEmpty
            ? Column(
                children: [
                  const SizedBox(height: 12),
                  ...controller.selectedBuddies.map((buddyUID) {
                    return _buildBuddyItem(buddyUID);
                  }),
                ],
              )
            : Container()),

        // 添加搭子按钮 - 增加间距
        Column(
          children: [
            const SizedBox(height: 12), // 从8增加到12，提升呼吸感
            _buildAddBuddyButton(context),
          ],
        ),
      ],
    );
  }

  /// 构建单个搭子项
  Widget _buildBuddyItem(String buddyUID) {
    final buddyUser = controller.getBuddyUser(buddyUID);

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        color: const Color(0xFFFAFAFA),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFFF0F0F0),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // 现代化头像
          _buildModernAvatar(buddyUser),
          const SizedBox(width: 10),
          // 搭子信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  buddyUser?.nickname ?? '用户 $buddyUID',
                  style: const TextStyle(
                    fontSize: 15,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF464646), // 与"习惯搭子"标题颜色一致
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis, // 处理昵称过长的情况
                ),
                const SizedBox(height: 3),
                _buildModernStatusTag(buddyUser?.status ?? BuddyStatus.accepted),
              ],
            ),
          ),
          // 现代化操作按钮
          _buildModernActionButton(buddyUID),
        ],
      ),
    );
  }

  /// 构建成功率标签
  Widget _buildSuccessRateTag() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Color(0xFFFF8E8E),
            Color(0xFFFF6B6B),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        '成功率+60%',
        style: TextStyle(
          color: Colors.white,
          fontSize: 11,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  /// 构建简洁头像
  Widget _buildModernAvatar(BuddyUser? user) {
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        color: _getSimpleAvatarColor(user?.uid ?? ""),
        borderRadius: BorderRadius.circular(8),
      ),
      child: user?.avatarUrl != null
          ? ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.network(
                user!.avatarUrl!,
                width: 40,
                height: 40,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return _buildAvatarFallback(user.nickname);
                },
              ),
            )
          : _buildAvatarFallback(user?.nickname ?? '用户'),
    );
  }

  /// 构建头像回退显示
  Widget _buildAvatarFallback(String name) {
    return Center(
      child: Text(
        name.isNotEmpty ? name[0].toUpperCase() : '用',
        style: const TextStyle(
          color: Colors.white,
          fontSize: 16,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  /// 根据用户ID生成简洁头像颜色
  Color _getSimpleAvatarColor(String userUID) {
    final colors = [
      const Color(0xFF8B9DC3), // 柔和蓝紫色
      const Color(0xFFDDB892), // 温暖米色
      const Color(0xFF95B8D1), // 柔和蓝色
      const Color(0xFFB8E6B8), // 柔和绿色
      const Color(0xFFE8A87C), // 柔和橙色
      const Color(0xFFC8A8E9), // 柔和紫色
      const Color(0xFFD4B5B0), // 柔和粉色
      const Color(0xFFA8C8A8), // 柔和薄荷绿
    ];

    final index = userUID.hashCode.abs() % colors.length;
    return colors[index];
  }

  /// 构建现代化状态标签
  Widget _buildModernStatusTag(BuddyStatus status) {
    Color backgroundColor;
    Color textColor;
    String text;

    switch (status) {
      case BuddyStatus.pending:
        backgroundColor = const Color(0xFFFEF3C7); // 更柔和的黄色背景
        textColor = const Color(0xFFB45309); // 更深的橙棕色文字
        text = '等待回应';
        break;
      case BuddyStatus.accepted:
        backgroundColor = const Color(0xFFDCFCE7); // 更清新的绿色背景
        textColor = const Color(0xFF166534); // 更深的绿色文字
        text = '已建立';
        break;
      case BuddyStatus.rejected:
        backgroundColor = const Color(0xFFFEE2E2); // 更柔和的红色背景
        textColor = const Color(0xFFDC2626); // 更明确的红色文字
        text = '已拒绝';
        break;
      case BuddyStatus.blocked:
        backgroundColor = const Color(0xFFF3F4F6); // 更现代的灰色背景
        textColor = const Color(0xFF6B7280); // 更柔和的灰色文字
        text = '已屏蔽';
        break;
      default:
        backgroundColor = const Color(0xFFF3F4F6);
        textColor = const Color(0xFF6B7280);
        text = '未知';
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 10,
          fontWeight: FontWeight.w500,
          color: textColor,
        ),
      ),
    );
  }

  /// 构建现代化操作按钮
  Widget _buildModernActionButton(String buddyUID) {
    return Container(
      width: 32,
      height: 32,
      decoration: BoxDecoration(
        color: const Color(0xFFF7FAFC),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: const Color(0xFFE2E8F0),
          width: 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(8),
          onTap: () => controller.removeBuddy(buddyUID),
          child: const Icon(
            Icons.close,
            size: 16,
            color: Color(0xFF718096),
          ),
        ),
      ),
    );
  }

  /// 构建添加搭子按钮
  Widget _buildAddBuddyButton(BuildContext context) {
    return GestureDetector(
      onTap: () => controller.showBuddySearchBottomSheet(context),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: AppColors.primary.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: AppColors.primary.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                color: AppColors.primary,
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.add,
                size: 12,
                color: Colors.white,
              ),
            ),
            const SizedBox(width: 8),
            Text(
              '邀请习惯搭子'.tr,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: AppColors.primary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建通知权限提示
  Widget _buildNotificationPermissionTip() {
    return GestureDetector(
      onTap: () {
        // TODO: 实现打开通知权限设置的功能
        // 这里暂时只是一个占位，后续可以添加跳转到系统设置的逻辑
        showErrorMessage(HiConstants.errorTypeOther, '请在系统设置中开启通知权限'.tr);
      },
      child: Row(
        children: [
          Text(
            '提醒未开启，'.tr,
            style: TextStyle(
              fontSize: 13,
              color: Colors.grey.shade600,
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(
            '点击打开通知功能'.tr,
            style: TextStyle(
              fontSize: 13,
              color: Color(0xFFE86144),
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  // Advanced Options Toggle Button
  Widget _buildAdvancedOptionsToggle(BuildContext context) {
    return Obx(() => InkWell(
          onTap: controller.toggleAdvancedOptions,
          borderRadius: BorderRadius.circular(12),
          child: Container(
            margin:  EdgeInsets.only(bottom: _getAdaptiveCardSpacing(context)),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: const Color(0xFFF0F0F0), width: 1),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.04),
                  blurRadius: 4,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Icon(
                    Icons.tune,
                    color: AppColors.primary,
                    size: 18,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '更多设置'.tr,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: Color(0xFF464646),
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        controller.showAdvancedOptions.value
                            ? '隐私保护、严格模式等高级选项'.tr
                            : '点击展开隐私保护、严格模式等选项'.tr,
                        style: const TextStyle(
                          fontSize: 13,
                          color: Color(0xFF666666),
                        ),
                      ),
                    ],
                  ),
                ),
                AnimatedRotation(
                  turns: controller.showAdvancedOptions.value ? 0.5 : 0,
                  duration: const Duration(milliseconds: 200),
                  child: Icon(
                    Icons.keyboard_arrow_down,
                    color: AppColors.primary,
                    size: 20,
                  ),
                ),
              ],
            ),
          ),
        ));
  }

  // Advanced Options Card
  Widget _buildAdvancedOptionsCard() {
    return Obx(() => AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          height: controller.showAdvancedOptions.value ? null : 0,
          child: controller.showAdvancedOptions.value
              ? _buildCard(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildSwitchOption(
                        title: '隐私保护',
                        subtitle: '使用通用名称代替习惯真实内容（限时免费）',
                        valueSwitch: controller.privacySwitch,
                        switchController: controller.privacyController,
                        onToggle: controller.togglePrivacy,
                      ),
                      // 仅当隐私保护开关打开时显示的选项
                      Obx(() => !controller.privacySwitch.value
                          ? const SizedBox.shrink()
                          : AnimatedContainer(
                              duration: const Duration(milliseconds: 150),
                              margin: const EdgeInsets.only(top: 16),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    '显示模式'.tr,
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: Color(0xFF6B7280),
                                    ),
                                  ),
                                  const SizedBox(height: 8),

                                  // 单选按钮组
                                  _buildPrivacyDisplayOption(
                                    title: '自定义名称',
                                    value: PrivacyDisplayType.custom,
                                    groupValue:
                                        controller.privacyDisplayType.value,
                                    onChanged:
                                        controller.updatePrivacyDisplayType,
                                  ),

                                  // 自定义名称选项被选中时显示输入框
                                  if (controller.privacyDisplayType.value ==
                                      PrivacyDisplayType.custom)
                                    Padding(
                                      padding: const EdgeInsets.only(
                                          left: 28.0, top: 8.0, bottom: 8.0),
                                      child: _buildTextField(
                                        controller: controller
                                            .customPrivacyNameController,
                                        hintText: '输入显示名称',
                                        fontSize: 14,
                                        height: 40,
                                      ),
                                    ),

                                  _buildPrivacyDisplayOption(
                                    title: '星号掩码',
                                    value: PrivacyDisplayType.stars,
                                    groupValue:
                                        controller.privacyDisplayType.value,
                                    onChanged:
                                        controller.updatePrivacyDisplayType,
                                  ),
                                ],
                              ),
                            )),
                      const SizedBox(height: 20),
                      // 用户体验组 - 高频使用功能
                      _buildSwitchOption(
                        title: '开启奖励',
                        subtitle: '完成习惯后展示激励',
                        valueSwitch: controller.rewardSwitch,
                        switchController: controller.rewardController,
                        onToggle: controller.toggleRewardsAndMotivation,
                      ),
                      const SizedBox(height: 16),
                      _buildSwitchOption(
                        title: '严格模式',
                        subtitle: '打卡后当天不可撤销',
                        valueSwitch: controller.strictModeSwitch,
                        switchController: controller.strictModeController,
                        onToggle: controller.toggleStrictMode,
                      ),

                      // 分组分隔 - 更大间距
                      const SizedBox(height: 28),

                      // 隐私安全组 - 低频设置功能
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  '结束日期'.tr,
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w500,
                                    color: Color(0xFF464646),
                                  ),
                                ),
                                SizedBox(height: 4),
                                Text(
                                  '习惯终止时间'.tr,
                                  style: TextStyle(
                                    fontSize: 13,
                                    color: Color(0xFF666666),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          SizedBox(
                            height: 28,
                            width: 50,
                            child: AdvancedSwitch(
                              enabled: true,
                              initialValue: controller.hasEndDate.value,
                              controller: controller.endDateController,
                              activeColor: AppColors.primary,
                              inactiveColor: Colors.grey.shade200,
                              onChanged: (value) {
                                controller.hasEndDate.value = value;
                              },
                            ),
                          ),
                        ],
                      ),
                      // End Date Selector (visible only when toggle is on)
                      Obx(() => !controller.hasEndDate.value
                          ? const SizedBox.shrink()
                          : AnimatedContainer(
                              duration: const Duration(milliseconds: 150),
                              margin: const EdgeInsets.only(top: 16),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  InkWell(
                                    onTap: () =>
                                        controller.showCustomDatePicker(),
                                    borderRadius: BorderRadius.circular(8),
                                    child: Container(
                                      width: double.infinity,
                                      height: 48,
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 16),
                                      decoration: BoxDecoration(
                                        color: const Color(0xFFF4F9F7),
                                        borderRadius: BorderRadius.circular(8),
                                        border: Border.all(
                                          color: const Color(0xFFEEEEEE),
                                          width: 1,
                                        ),
                                      ),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text(
                                            controller.endDate.value != null
                                                ? _formatDate(
                                                    controller.endDate.value!)
                                                : '选择结束日期'.tr,
                                            style: TextStyle(
                                              fontSize: 16,
                                              color: controller.endDate.value !=
                                                      null
                                                  ? const Color(0xFF333333)
                                                  : const Color(0xFF9E9E9E),
                                            ),
                                          ),
                                          const Icon(
                                            Icons.calendar_today_outlined,
                                            size: 20,
                                            color: Color(0xFF757575),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            )),
                    ],
                  ),
                )
              : const SizedBox.shrink(),
        ));
  }

  // Bottom Buttons
  Widget _buildBottomButtons() {
    return Padding(
      padding: const EdgeInsets.only(top: 16, bottom: 45),
      child: Row(
        children: [
          Expanded(
            child: TextButton(
              onPressed: controller.cancelCreation,
              style: TextButton.styleFrom(
                foregroundColor: const Color(0xFF464646),
                backgroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 8),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                  side: const BorderSide(color: Color(0xFFE0E0E0)),
                ),
              ),
              child: Text(
                '取消'.tr,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Obx(() => ElevatedButton(
                  onPressed: controller.isSubmitting.value
                      ? null
                      : controller.submitForm,
                  style: ElevatedButton.styleFrom(
                    foregroundColor: Colors.white,
                    backgroundColor: AppColors.primary,
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    elevation: 2,
                    shadowColor: Colors.black.withOpacity(0.15),
                    disabledBackgroundColor: AppColors.primary.withOpacity(0.6),
                  ),
                  child: controller.isSubmitting.value
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            color: Colors.white,
                            strokeWidth: 2,
                          ),
                        )
                      : Text(
                          '创建'.tr,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                )),
          ),
        ],
      ),
    );
  }

  // Card Container
  Widget _buildCard({required Widget child}) {
    return Container(
      margin: EdgeInsets.only(bottom: _getAdaptiveCardSpacing(Get.context!)),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: const Color(0xFFE2E8F0), width: 1),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.06),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.02),
            blurRadius: 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: child,
    );
  }

  // Text Field
  Widget _buildTextField({
    TextEditingController? controller,
    String? hintText,
    ValueChanged<String>? onChanged,
    List<TextInputFormatter>? inputFormatters,
    double fontSize = 16,
    double height = 48,
  }) {
    return Theme(
      data: Theme.of(Get.context!).copyWith(
        inputDecorationTheme: const InputDecorationTheme(
          focusedBorder: OutlineInputBorder(
            borderSide: BorderSide(color: AppColors.primary, width: 2),
            borderRadius: BorderRadius.all(Radius.circular(8)),
          ),
          enabledBorder: OutlineInputBorder(
            borderSide: BorderSide(color: Color(0xFFEEEEEE), width: 1),
            borderRadius: BorderRadius.all(Radius.circular(8)),
          ),
        ),
      ),
      child: Container(
        height: height,
        decoration: BoxDecoration(
          color: const Color(0xFFF8F8F8),
          borderRadius: BorderRadius.circular(8),
        ),
        child: TextField(
          controller: controller,
          onChanged: onChanged,
          inputFormatters: inputFormatters,
          decoration: InputDecoration(
            hintText: hintText?.tr,
            hintStyle: TextStyle(
              color: AppColors.textLight,
              fontSize: fontSize,
            ),
            contentPadding: EdgeInsets.symmetric(horizontal: fontSize),
            border: InputBorder.none,
            filled: true,
            fillColor: Colors.white,
          ),
          style: TextStyle(
            fontSize: fontSize,
            color: Color(0xFF333333),
          ),
        ),
      ),
    );
  }

  // Switch Option
  Widget _buildSwitchOption({
    required String title,
    required String subtitle,
    required RxBool valueSwitch,
    required ValueNotifier<bool> switchController,
    required Function() onToggle,
    IconData? icon,
    Color? iconColor,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  if (icon != null) ...[
                    Icon(
                      icon,
                      color: iconColor ?? AppColors.primary,
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                  ],
                  Text(
                    title.tr,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: Color(0xFF464646),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Text(
                subtitle.tr,
                overflow: TextOverflow.ellipsis,
                maxLines: 2,
                style: const TextStyle(
                  fontSize: 13,
                  color: Color(0xFF666666),
                ),
              ),
            ],
          ),
        ),
        Container(
          height: 28,
          width: 70,
          padding: const EdgeInsets.only(left: 20),
          child: AdvancedSwitch(
            enabled: true,
            initialValue: valueSwitch.value,
            controller: switchController,
            activeColor: AppColors.primary,
            inactiveColor: Colors.grey.shade200,
            onChanged: (_) => onToggle(),
          ),
        ),
      ],
    );
  }

  // Habit Type Button
  Widget _buildTypeButton(int type, String label, {IconData? icon}) {
    return Obx(() {
      final isSelected = controller.habitType.value == type;
      return InkWell(
        onTap: () => controller.setHabitType(type),
        borderRadius: BorderRadius.circular(12),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          height: 48,
          decoration: BoxDecoration(
            color: isSelected ? AppColors.primary : const Color(0xFFFAFAFA),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isSelected ? AppColors.primary : const Color(0xFFE2E8F0),
              width: isSelected ? 2 : 1,
            ),
            boxShadow: isSelected
                ? [
                    BoxShadow(
                      color: AppColors.primary.withOpacity(0.25),
                      offset: const Offset(0, 2),
                      blurRadius: 8,
                    ),
                    BoxShadow(
                      color: AppColors.primary.withOpacity(0.1),
                      offset: const Offset(0, 1),
                      blurRadius: 2,
                    ),
                  ]
                : [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.02),
                      offset: const Offset(0, 1),
                      blurRadius: 2,
                    ),
                  ],
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (icon != null) ...[
                Icon(
                  icon,
                  size: 16,
                  color: isSelected ? Colors.white : const Color(0xFF4A5568),
                ),
                const SizedBox(width: 8),
              ],
              Flexible(
                child: Text(
                  label.tr,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                    color: isSelected ? Colors.white : const Color(0xFF666666),
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
        ),
      );
    });
  }

  // Cycle Selector
  Widget _buildCycleSelector() {
    return Container(
      height: 40,
      decoration: BoxDecoration(
        color: const Color(0xFFF4F9F7),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          _buildCycleOption(habitPunchCycleFix, '固定'),
          _buildCycleOption(habitPunchCycleWeek, '按周'),
          _buildCycleOption(habitPunchCycleMonth, '按月'),
        ],
      ),
    );
  }

  // 跟踪方式选择器 - 轻量化设计，平衡美观与实用性
  Widget _buildRecordSelector() {
    return Container(
      height: 52,
      decoration: BoxDecoration(
        color: const Color(0xFFFBFBFB), // 更浅的背景色
        borderRadius: BorderRadius.circular(8), // 更小的圆角
        border: Border.all(
          color: const Color(0xFFF0F0F0), // 更浅的边框
          width: 0.5, // 更细的边框
        ),
      ),
      child: Row(
        children: [
          _buildRecordOption(recordTypeTime, '时间', '记录时刻'),
          _buildRecordOption(recordTypeDuration, '时长', '持续时间'),
          _buildRecordOption(recordTypeCount, '次数', '发生次数'),
        ],
      ),
    );
  }

  // 轻量化设计的选项样式
  Widget _buildRecordOption(int recordType, String label, String labelDesc) {
    return Expanded(
      child: Obx(() {
        final isSelected = controller.record.value == recordType;
        return InkWell(
          onTap: () => controller.setRecord(recordType),
          highlightColor: Colors.transparent, // 禁用高亮效果
          splashColor: Colors.transparent, // 禁用溅墨效果
          borderRadius: BorderRadius.circular(8),
          child: Container(
            height: 52,
            alignment: Alignment.center,
            margin: const EdgeInsets.all(3), // 减少边距避免溢出
            decoration: BoxDecoration(
              color: isSelected ? Colors.white : Colors.transparent,
              borderRadius: BorderRadius.circular(6), // 更小的圆角，与外层容器协调
              border: isSelected
                  ? Border.all(
                      color: AppColors.primary,
                      width: 1,
                    )
                  : null,
            ),
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 2, horizontal: 6), // 减少内边距
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min, // 重要：限制Column的大小
                children: [
                  Text(
                    label.tr,
                    style: TextStyle(
                      fontSize: 14, // 调整字体大小避免溢出
                      fontWeight: isSelected ? FontWeight.w500 : FontWeight.w400,
                      color: isSelected
                          ? const Color(0xFF464646)  // 统一选中色：深灰色
                          : const Color(0xFF666666), // 统一未选中色：中等灰色
                    ),
                  ),
                  const SizedBox(height: 1), // 减少间距
                  Text(
                    labelDesc.tr,
                    style: TextStyle(
                      fontSize: 10, // 调整字体大小避免溢出
                      fontWeight: FontWeight.w400,
                      color: isSelected
                          ? const Color(0xFF666666)  // 选中时副标题用中等灰色
                          : const Color(0xFF888888), // 未选中时副标题用浅灰色
                    ),
                    textAlign: TextAlign.center, // 确保文字居中
                    maxLines: 1, // 限制为单行，避免溢出
                    overflow: TextOverflow.ellipsis, // 添加溢出处理
                  ),
                ],
              ),
            ),
          ),
        );
      }),
    );
  }

  // Cycle Option
  Widget _buildCycleOption(int cycleType, String label) {
    return Expanded(
      child: Obx(() {
        final isSelected = controller.cycle.value == cycleType;
        return InkWell(
          onTap: () => controller.setCycle(cycleType),
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            height: 40,
            alignment: Alignment.center,
            decoration: BoxDecoration(
              color: isSelected ? Colors.white : null,
              borderRadius: BorderRadius.circular(8),
              border: isSelected
                  ? const Border(
                      bottom: BorderSide(
                        color: AppColors.primary,
                        width: 2,
                      ),
                    )
                  : null,
            ),
            child: Text(
              label.tr,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: isSelected
                    ? const Color(0xFF464646)
                    : const Color(0xFF666666),
              ),
            ),
          ),
        );
      }),
    );
  }

  // Day Selector
  Widget _buildDaySelector() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children:
          controller.availableDays.map((day) => _buildDayButton(day)).toList(),
    );
  }

  // Day Button
  Widget _buildDayButton(String day) {
    return Obx(() {
      int dayInt = controller.dayIntMap[day] ?? 0;
      final isSelected = controller.selectedDays.contains(dayInt);
      return InkWell(
        onTap: () => controller.toggleDay(dayInt),
        borderRadius: BorderRadius.circular(24),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          width: 44,
          height: 44,
          alignment: Alignment.center,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: isSelected ? AppColors.primary : const Color(0xFFFAFAFA),
            border: Border.all(
              color: isSelected ? AppColors.primary : const Color(0xFFE2E8F0),
              width: isSelected ? 2 : 1,
            ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.04), // 非常淡的阴影
                  offset: Offset(0, 1),
                  blurRadius: 2,
                ),
              ],
          ),
          child: AnimatedScale(
            duration: const Duration(milliseconds: 150),
            scale: isSelected ? 1.0 : 0.95,
            child: Text(
              day.tr,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: isSelected ? Colors.white : Color(0xFF64748B),
              ),
            ),
          ),
        ),
      );
    });
  }

  // Counter Control
  Widget _buildCounterControl() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Minus button
        Material(
          color: Colors.transparent,
          borderRadius: BorderRadius.circular(10),
          child: InkWell(
            borderRadius: BorderRadius.circular(10),
            onTap: controller.decrementCheckInTimes,
            child: Container(
              width: 36,
              height: 36,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                color: Colors.white,
                border: Border.all(color: const Color(0xFFE2E8F0), width: 1),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.04),
                    offset: const Offset(0, 1),
                    blurRadius: 2,
                  ),
                ],
              ),
              child: const Icon(
                CustomFonts.removeWithoutBorder,
                size: 16,
                color: Color(0xFF666666),
              ),
            ),
          ),
        ),
        // Number display
        Obx(() {
          return Container(
            width: 60,
            alignment: Alignment.center,
            child: Text(
              '${controller.checkInTimes.value}',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Color(0xFF464646),
              ),
            ),
          );
        }),
        // Plus button
        Material(
          color: Colors.transparent,
          borderRadius: BorderRadius.circular(10),
          child: InkWell(
            borderRadius: BorderRadius.circular(10),
            onTap: controller.incrementCheckInTimes,
            child: Container(
              width: 36,
              height: 36,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                color: Colors.white,
                border: Border.all(color: const Color(0xFFE2E8F0), width: 1),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.04),
                    offset: const Offset(0, 1),
                    blurRadius: 2,
                  ),
                ],
              ),
              child: const Icon(
                CustomFonts.addWithoutBorder,
                size: 16,
                color: Color(0xFF666666),
              ),
            ),
          ),
        ),
      ],
    );
  }

  // Custom Counter Control with specific controllers
  Widget _buildCustomCounterControl({
    required Function() increment,
    required Function() decrement,
    required RxInt value,
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Minus button
        Material(
          color: Colors.transparent,
          borderRadius: BorderRadius.circular(10),
          child: InkWell(
            borderRadius: BorderRadius.circular(10),
            onTap: decrement,
            child: Container(
              width: 36,
              height: 36,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                color: Colors.white,
                border: Border.all(color: const Color(0xFFE2E8F0), width: 1),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.04),
                    offset: const Offset(0, 1),
                    blurRadius: 2,
                  ),
                ],
              ),
              child: const Icon(
                CustomFonts.removeWithoutBorder,
                size: 18,
                color: Color(0xFF666666),
              ),
            ),
          ),
        ),
        // Number display
        Container(
          width: 60,
          alignment: Alignment.center,
          child: Text(
            '${value.value}',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Color(0xFF464646),
            ),
          ),
        ),
        // Plus button
        Material(
          color: Colors.transparent,
          borderRadius: BorderRadius.circular(10),
          child: InkWell(
            borderRadius: BorderRadius.circular(10),
            onTap: increment,
            child: Container(
              width: 36,
              height: 36,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                color: Colors.white,
                border: Border.all(color: const Color(0xFFE2E8F0), width: 1),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.04),
                    offset: const Offset(0, 1),
                    blurRadius: 2,
                  ),
                ],
              ),
              child: const Icon(
                CustomFonts.addWithoutBorder,
                size: 16,
                color: Color(0xFF666666),
              ),
            ),
          ),
        ),
      ],
    );
  }

  // Format date to display as "Month Day, Year"
  String _formatDate(DateTime date) {
    // 检查当前语言环境
    final locale = Get.locale?.languageCode ?? 'zh';

    if (locale == 'zh') {
      // 中文格式：年月日
      return '${date.year}年${date.month}月${date.day}日';
    } else {
      // 英文格式：月 日, 年
      final months = [
        'January',
        'February',
        'March',
        'April',
        'May',
        'June',
        'July',
        'August',
        'September',
        'October',
        'November',
        'December'
      ];
      final month = months[date.month - 1];
      final day = _getDayWithSuffix(date.day);
      return '$month $day, ${date.year}';
    }
  }

  // Get day with suffix (1st, 2nd, 3rd, etc.)
  String _getDayWithSuffix(int day) {
    // 检查当前语言环境
    final locale = Get.locale?.languageCode ?? 'zh';

    if (locale == 'zh') {
      // 中文环境下不添加后缀
      return '$day';
    } else {
      // 英文环境下添加后缀
      if (day >= 11 && day <= 13) {
        return '${day}th';
      }

      switch (day % 10) {
        case 1:
          return '${day}st';
        case 2:
          return '${day}nd';
        case 3:
          return '${day}rd';
        default:
          return '${day}th';
      }
    }
  }

  // 隐私显示选项的单选按钮
  Widget _buildPrivacyDisplayOption({
    required String title,
    required PrivacyDisplayType value,
    required PrivacyDisplayType groupValue,
    required Function(PrivacyDisplayType?) onChanged,
  }) {
    return InkWell(
      onTap: () => onChanged(value),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 6.0),
        child: Row(
          children: [
            Radio<PrivacyDisplayType>(
              value: value,
              groupValue: groupValue,
              onChanged: onChanged,
              activeColor: AppColors.primary,
              fillColor: WidgetStateProperty.resolveWith<Color>((states) {
                if (states.contains(WidgetState.selected)) {
                  return AppColors.primary; // 选中状态颜色
                }
                return const Color(0xFF757575); // 未选中状态颜色
              }),
              materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
            const SizedBox(width: 8),
            Text(
              title.tr,
              style: TextStyle(
                fontSize: 14,
                color: Color(0xFF374151),
              ),
            ),
          ],
        ),
      ),
    );
  }


}
