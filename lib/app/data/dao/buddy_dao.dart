import '../../models/buddy_model.dart';
import '../../utils/hi_net.dart';
import '../request/buddy_request.dart';

/// 搭子数据访问层
/// 负责处理搭子相关的数据操作，包括网络请求和本地缓存
class BuddyDao {
  /// 通过 UID 搜索搭子用户
  /// 根据 UID 精确搜索，不使用分页，理论上只会找到一个用户
  static Future<BuddySearchResult> searchBuddiesByUid({
    required String uid,
  }) async {
    SearchBuddiesRequest request = SearchBuddiesRequest();
    request.add("uid", uid);
    var result = await HiNet.getInstance().fire(request);
    return BuddySearchResult.fromJson(result['data']);
  }

  /// 发送搭子邀请
  static Future<void> sendBuddyInvitation({
    required String toUserUID,
    String? message,
  }) async {
    SendBuddyInvitationRequest request = SendBuddyInvitationRequest();
    request.add("to_user_uid", toUserUID);
    if (message != null) {
      request.add("message", message);
    }
    await HiNet.getInstance().fire(request);
    // 没有异常就表示成功，不需要检查返回值
  }
}

/// BuddyRelationship 扩展方法
extension BuddyRelationshipExtension on BuddyRelationship {
  BuddyRelationship copyWith({
    String? id,
    String? userId,
    String? buddyId,
    BuddyUser? buddy,
    DateTime? createdAt,
    bool? isActive,
    int? mutualHabits,
  }) {
    return BuddyRelationship(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      buddyId: buddyId ?? this.buddyId,
      buddy: buddy ?? this.buddy,
      createdAt: createdAt ?? this.createdAt,
      isActive: isActive ?? this.isActive,
      mutualHabits: mutualHabits ?? this.mutualHabits,
    );
  }
}
