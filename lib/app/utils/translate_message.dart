import 'package:get/get.dart';

class Messages extends Translations {
  @override
  Map<String, Map<String, String>> get keys => {
        'zh_CN': {},
        'en_US': {
          // 未登录首页
          "培养更好的习惯，改变你的生活": 'Build Better Habits, Transform Your Life',
          "科学方法培养持久习惯": 'Science-based methods for building lasting habits',
          "福格行为模型": 'Fogg Method',
          "微习惯理念": 'Micro-Habit Technique',
          "数据驱动": 'Data-Driven',
          "登录查看你的习惯": 'Sign in to view your habits',
          "解锁你的个性化习惯追踪体验": 'Unlock your personalized habit tracking experience',
          "登录": 'Sign In',
          "30 秒即可开始": 'Get started in 30 seconds',
          "快速登录": 'Quick Sign In',
          "使用谷歌账户登录": 'Sign in with Google',
          "使用Apple账户登录": 'Sign in with Apple',
          "使用上述服务意味着您同意我们的 ": 'By continuing, you agree to our ',
          "或继续使用": "or continue with",
          "服务条款": "Terms",

          // 成就弹窗相关翻译
          "描述": "Description",
          "解锁条件": "Requirement",
          "解锁时间": "Unlocked",
          "已解锁": "Unlocked",
          "未解锁": "Locked",
          "进度": "Progress",
          "已完成": "complete",

          "全部": 'All',
          "未完成": 'To Do',
          // More common than "Undo" for task status
          "已完成": 'Completed',
          // 与"共完成"保持一致，更简短
          "首页": 'Home',
          '星球': 'Social',
          // 与"社区"翻译保持一致
          "发现": 'Discover',
          "我的": 'Me',
          // Standard for personal space
          '随记': 'Note',
          // More dynamic than "Memo"
          '打卡': 'Check-in',
          // Standard for daily sign-in features
          '计时': 'Timer',
          '计时中': 'Active',
          // Dynamic status indicator with ellipsis to indicate ongoing status
          '微习惯': 'Micro',
          // Emerging standard terminology
          '普通习惯': 'Regular',
          "频繁超额完成": 'Top Habits',
          // User status label
          "频繁未完成": 'Low Habits',
          // Modern app convention
          "登录后可查看更多内容": 'Sign in for more content',
          // Concise version
          "星期一": "Monday",
          "星期二": "Tuesday",
          "星期三": "Wednesday",
          "星期四": "Thursday",
          "星期五": "Friday",
          "星期六": "Saturday",
          "星期日": "Sunday",
          "习惯管理": "Habit Management",
          // 强调管理功能
          "激励": "Reward Management",
          // 更符合激励体系表达
          "登录/注册": "Sign In/Up",
          // 保持与之前"Sign in"的术语统一
          "设置": "Settings",
          // 使用复数形式
          "帮助与反馈": "Help & Feedback",
          // 完整保留两个功能点
          "请先登录": "Sign In Required",
          // 更符合权限提示规范
          "登录后可同步多端数据": "Sync Across Devices",
          // 简化表达
          "未注册邮箱或手机验证后自动登录": "Auto Sign Up",
          // 明确核心流程
          "密码登录": "Password",
          "请输入邮箱或手机号码": "Email/Phone",
          // 输入框占位符优化
          "获取验证码": "Get Code",
          // 移动端常用简写
          "获取中...": "Sending...",
          // 动态过程指示
          "我已阅读并同意 ": "I agree to ",
          // 协议勾选标准句式
          "用户协议": "Terms",
          // 国际通用表述
          " 和 ": " and ",
          // 保留空格确保格式
          "隐私政策": "Privacy Policy",
          "请先勾选并同意相关协议": "Accept terms",
          // 强引导性提示
          "输入正确密码登录": "Password Login",
          "验证码登录": "Code",
          // 延续"Code"术语体系（与"Get Code"对应）
          "请输入登录密码": "Password",
          // 输入框占位符优化（去动词，如之前"Phone Number"）
          "登录中...": "Signing In...",
          "请输入验证码": "Enter Code",
          // 输入框占位符 (与"Phone Number"风格一致)
          "验证码已发送到": "Code sent to",
          // 动态插值占位符
          "重新获取": "Resend",
          "创建": "Create",
          "习惯名称不能为空": "Habit name required",
          "阶段名称不能为空": "Phase name required",
          "跟踪方式": "Analytics",
          "「统计方式」说明": "Analytics Guide",
          "支持时间、时长和次数三个维度的统计分析":
              "Track your habits across three key metrics: timing, duration, and frequency",
          "时间统计：": "Timing Metrics: ",
          "最早打卡时间、最晚打卡时间、平均打卡时间。":
              "Earliest, latest, and average check-in times.",
          "时长统计：": "Duration Metrics: ",
          "最短打卡时长、最长打卡时长、平均打卡时长。":
              "Shortest, longest, and average session lengths.",
          "次数统计：": "Frequency Metrics: ",
          "最少打卡次数、最多打卡次数、平均打卡次数。":
              "Minimum, maximum, and average number of completions.",
          "建议使用方法：": "How to Use: \n",
          "创建记录类型习惯 -> 持续打卡一到两周 -> 根据数据制定合理的微习惯 -> 不断进行调整微习惯难度。":
              "1. Create a trackable habit\n2. Log consistently for 1-2 weeks\n3. Design micro-habits based on your data\n4. Gradually adjust difficulty levels",
          "踏上自我发现之路": "Begin Your Journey",
          "时间": "Time",
          // 时间统计维度
          "时长": "Duration",
          "次数": "Count",
          // 次数统计（比 Frequency 更直观）
          "锻炼 21 天": "21-Day Challenge",
          // 增加挑战性表述
          "每天 1 个俯卧撑": "1 Push-up Daily",
          // 强调每日性
          "锻炼 10 分钟": "10-Min Workout",
          // 分钟简写
          "记录": "Journal",
          "次": "Times",
          // 动词形式更符合操作场景
          "习惯类型": "Habit Type",
          "「微习惯」说明": "Micro Habits Guide",
          "设计中引入了微习惯的理念：": "The power of micro habits: ",
          "简单才能改变行为。": "Simplicity is the key to change.",
          "由于动机和意志力是不可靠的，让改变保持微小，用确定的步骤去替代不可靠，这样才能让改变持续发生。":
              "Small, consistent steps overcome unreliable motivation and willpower, creating lasting change.",
          "微习惯分为两个阶段：": "Two-stage approach: ",
          "阶段一（简单）": "Stage 1 (Easy)",
          "和 ": " and ",
          "阶段二（稍微有点难度）": "Stage 2 (Slightly Challenging)",
          "但如何设计简单的阶段呢？": "How to get started?\n",
          "只是因为你不了解自己，试试在名称": "Track first, change later. Add ",
          "最前面输入记录": "Log",
          "，通过数据去认识自己吧。": " to habit names to collect data about yourself.",
          "不管多小的一步，都算数": "Every tiny step counts",
          "。无论你完成哪个阶段，习惯都算完成。":
              ".Completing either stage counts as finishing the habit.",
          "阶段一": "Stage 1",
          // 使用 Stage 比 Phase 更通用
          "阶段二": "Stage 2",
          "打卡频率": "Frequency",
          "固定": "Daily",
          // 表示自定义周期
          "按周": "Weekly",
          // 标准周期术语
          "按月": "Monthly",
          "选择星期": "Select Days",
          "日": "Sun",
          "一": "Mon",
          "二": "Tue",
          "三": "Wed",
          "四": "Thu",
          "五": "Fri",
          "六": "Sat",
          "每天打卡": "Daily Check-in",
          "每周打卡": "Weekly Check-in",
          "每月打卡": "Monthly Check-in",
          "每周打卡几天": "How Many Days Per Week?",
          "每月打卡几天": "How Many Days Per Month?",
          "目标天数": "Target Days",
          "每天打卡几次": "Daily Completions",
          "每日次数": "Daily Frequency",
          "天": "Days",
          "打卡后不可撤销": "Irreversible Check-ins",
          "「打卡后不可撤销」说明": "Irreversible Check-ins Policy",
          "一旦用户完成打卡，就不允许撤销。由于人意志力的波动性，我们希望用户在意志力高涨时做出承诺，并在意志力低落时依然坚持这些承诺。打卡不仅是对习惯的记录，更是对":
              "Once checked in, you cannot undo it. This helps you stay committed even when willpower is low. Check-ins represent your ",
          "自我的承诺和诚实": "personal commitment",
          "的体现。": ".",
          "适用场景：": "Best For: ",
          "适用于想要戒除掉的习惯。": "Breaking unwanted habits.",
          "将事情本身（如不吃糖果）作为提示": "Example:",
          "，想象一下，你正在努力戒掉吃糖果的习惯。每天，你都会在「Life Habit」中打卡，承诺自己不再吃糖果。某天，你提前打卡了，承诺今天不吃糖果。到了下午，你突然很想吃一块糖果，但你想起了早上在「Life Habit」中做出的承诺。这个承诺成为了你坚守的理由，因为你知道，打卡后是无法撤销的。":
              " You're trying to quit candy. When cravings hit in the afternoon, your morning check-in becomes your motivation to resist, knowing it cannot be reversed.",
          "注意：设置为打卡后不可撤销后，不允许再次修改。":
              "Note: Once this setting is enabled, it cannot be changed later.",
          "赶快试试吧": "Get Started",
          "参与激励": "Rewards & Motivation",
          "「参与激励」说明": "About Rewards",
          "这是一个有趣的功能，满足人内心的精神需要。":
              "A fun feature that provides visual rewards for your achievements.",
          "使用方法：": "How it works: ",
          "完成当天所有必须完成的激励习惯后，激励图片将自动弹出。":
              "Reward images automatically appear when you complete all your daily required habits.",
          "注意：": "Note: ",
          "需要在": "You need to set up reward images in",
          " 发现": " Discover",
          "激励 ": " Rewards ",
          "中配置激励图片才能生效。": " section for this feature to work.",
          "「频繁超额完成」习惯": "Top Habits",
          // 更准确地描述这类习惯
          "知道了": "Got it",
          "我知道了": "Got it",
          // 常用确认按钮文本
          "「频繁未完成」习惯": "Low Habits",
          // 更专业的表述
          "必": "R",
          // 更清晰的询问
          "否": "No",
          // 标准否定回答
          "是": "Yes",
          // 标准肯定回答
          "当前仅允许一个任务进行计时": "One timer only",
          // 更符合应用提示语
          "请输入内容...": "Share your thoughts...",
          // 标准输入框提示
          "保存": "Save",
          // 标准保存按钮
          "不允许取消打卡": "No cancel",
          // 明确的操作限制提示
          "进行中": "Active",
          // 标准状态指示
          "暂停": "Pause",
          // 标准控制按钮
          "结束": "End",
          // 标准控制按钮
          "成功": "Success",
          // 标准操作结果
          "超过一周不允许补卡": "No late entries",
          // 更符合应用提示语
          "打卡成功": "Success",
          // 标准操作结果
          "超过一周不允许取消打卡": "No late cancels",
          // 更符合应用提示语
          "取消打卡成功": "Canceled",
          // 标准操作结果
          "确定删除该习惯吗？": "Delete habit?",
          // 更清晰的询问
          "删除后不可恢复": "Can't undo",
          // 明确的操作限制提示
          "确定结束该习惯吗？": "Complete habit?",
          // 更清晰的询问
          "结束后不能重新开始": "No restart",
          // 明确的操作限制提示
          "打卡统计": "Stats",
          // 标准统计标题
          "完成率": "Rate",
          // 标准统计项
          "平均时间": "Avg Time",
          // 标准统计项
          "最早时间": "Early",
          // 标准统计项
          "最晚时间": "Late",
          // 标准统计项
          "平均时长": "Avg Dur",
          // 标准统计项
          "最短时长": "Min Dur",
          // 标准统计项
          "最长时长": "Max Dur",
          // 标准统计项
          "平均次数": "Avg Count",
          // 标准统计项
          "最少次数": "Min Count",
          // 标准统计项
          "最多次数": "Max Count",
          // 标准统计项
          "最长连续": "Best Streak",
          // 标准统计项
          "连续时间段": "Streak",
          // 标准统计项
          "时间轴": "Timeline",
          // 标准统计标题
          "阶段名称": "Phase Name",
          // 标准阶段标题
          "取消": "Cancel",
          // 标准取消按钮
          "新建计时": "New Timer",
          // 标准新建按钮
          "新建随记": "New Note",
          // 标准新建按钮
          "时长: ": "Duration: ",
          // 标准时间标题
          "更改打卡": "Edit Check-in",
          // 更简短的按钮文本
          "删除": "Delete",
          // 标准删除按钮
          "提交": "Submit",
          // 标准提交按钮
          "完成": "Complete",
          // 标准完成按钮
          "更改随记": "Edit Note",
          // 更简短的按钮文本
          "编辑": "Edit",
          // 标准编辑按钮
          "习惯已结束，不允许编辑": "No edits",
          // 明确的操作限制提示
          "今天": "Today",
          // 时间指示
          "昨天": "Yesterday",
          // 时间指示
          "明天": "Tomorrow",
          // 时间指示
          "本周": "Week",
          // 时间范围
          "上周": "Last Wk",
          // 时间范围
          "下周": "Next Wk",
          // 时间范围
          "本月": "Month",
          // 时间范围
          "上月": "Last Mo",
          // 时间范围
          "下月": "Next Mo",
          // 时间范围
          "社区": "Social",
          // 比"Planets"更准确的社交功能名称
          "分享": "Share",
          // 标准分享功能
          "分享应用": "Share App",
          // 分享应用功能
          "推荐给朋友和家人": "Recommend to friends and family",
          // 分享应用副标题
          "使用提示": "Usage Tip",
          // 确认按钮
          "不再提示": "Don't show again",
          // 不再提示按钮
          "复制": "Copy",
          // 标准复制功能
          "提醒": "Reminder",
          // 提醒功能
          "通知": "Alerts",
          // 更符合任务状态的表达
          "开始": "Start",
          // 标准开始按钮
          "继续": "Continue",
          // 标准继续按钮
          "共完成": "Completed",
          "每天": "Daily",
          "每周": "Weekly",
          "每月": "Monthly",
          "当周": "Week",
          "当月": "Month",
          "所有": "All",
          "补卡": "Make Up",
          "更改计时": "Edit Timer",
          "预览": "Preview",
          "更换": "Replace",
          "我们的激励功能旨在": "Our incentive function aims to ",
          "满足人们内心深处的需求": "satisfy people's deep inner needs",
          "，不仅限于感官的享受。无论是一段文字、一张照片、一段音频，还是一个视频，它们都可以成为激励你的源泉，满足你的精神需求。我们相信，":
              ", not limited to sensory enjoyment. Whether it's a piece of text, a photo, an audio clip, or a video, they can all become a source of motivation for you, satisfying your spiritual needs. We believe that,",
          "改变需要被他们看见": " change needs to be seen by them",
          "，无论他们是在现在、过去还是未来。我们希望这些激励能见证你的成长和改变。":
              ", whether they are in the present, past, or future. We hope these incentives will witness your growth and change.",
          "想象一下，当你完成了当天所有的习惯，看到那张想见的照片，听到那段想听的声音，你感受到一种被看见的满足。那些你在乎的人仿佛就在身边，默默地见证着你的每一次努力和成长。":
              "Imagine, when you have completed all your habits for the day, seeing the photo you wanted to see, hearing the sound you wanted to hear, you feel a sense of satisfaction of being seen. Those you care about seem to be by your side, silently witnessing your every effort and growth.",
          "通过这些激励功能，我们希望你在追求改变的路上感受到支持和陪伴。无论是通过文字、图像、声音还是视频，这些激励都在无声地陪伴着你，帮助你":
              "Through these incentive functions, we hope you feel supported and accompanied on your journey of pursuing change. Whether through text, images, sounds, or videos, these incentives silently accompany you, helping you to",
          "成为更好的自己。": " become a better version of yourself.",
          "注意：当前只支持图片": "Note: Currently only images are supported",
          "，更多功能正在开发中，敬请期待。":
              ", more features are under development, please stay tuned.",
          "（当日仅弹出一次）": " (pops up only once per day)",
          "账号管理": "Account",
          "关于我们": "About",
          "退出登录": "Sign Out",
          "退出成功": "Signed Out",
          "意见反馈": "Feedback",
          "问题和意见": "Your Message",
          "联系我们": "Contact",
          "使用邮箱联系我们：": "Email: ",
          "手机号码": "Phone",
          "修改手机号": "Change Phone",
          "手机号": "Phone",
          "验证码": "Code",
          "更改": "Change",
          "密码": "Password",
          "已设置": "Set",
          "未设置": "Not Set",
          "修改密码": "Change Password",
          "旧密码": "Old Password",
          "新密码（至少 8 位）": "New Password (8+)",
          "再次输入新密码": "Confirm Password",
          "忘记旧密码": "Forgot Password",
          "确认": "Confirm",
          "注销账号": "Delete Account",
          "删除账号": "Delete Account",
          "账号": "Account",
          "忘记密码": "Forgot Password",
          "个人信息": "My Profile",
          "未设置简介...": "No bio...",
          "头像": "Avatar",
          "昵称": "Nickname",
          "个人简介": "Bio",
          "请输入昵称": "Enter nickname",
          "请输入简介": "Enter bio",
          "编辑昵称": "Edit Nickname",
          "编辑简介": "Edit Bio",
          "昵称不能为空": "Nickname cannot be empty",
          "昵称更新成功": "Nickname updated",
          "简介更新成功": "Bio updated",
          "输入隐私密码": "Enter PIN",
          "密码错误": "Wrong password",
          "设置密码": "Set PIN",
          "请再次输入密码": "Confirm PIN",
          "请牢记你的密码，忘记后无法找回": "Remember PIN, cannot recover",
          "两次密码不一致": "PINs don't match",
          "请输入密码": "Enter PIN",
          "隐私习惯锁": "Privacy Lock",
          "密码锁": "PIN Lock",
          "非会员不支持": "Members Only",
          """此操作无法撤销。
此操作将永久删除你的整个账户和所有相关数据。
请输入你的注册账号和密码进行确认。""": """This action cannot be undone.
Your account and all data will be permanently deleted.
Enter your account and password to confirm.""",
          "服务繁忙，请稍后再试": "Server busy. Please try again later.",
          "参数错误": "Invalid input.",
          "密码不符合规范": "Password doesn't meet requirements.",
          "习惯「暂停」失败": "Couldn't pause habit.",
          "手机号已注册": "Phone number already registered.",
          "手机号已被使用": "This phone number is already in use.",
          "习惯「计时」失败": "Timer couldn't start.",
          "习惯「恢复」失败": "Couldn't resume habit.",
          "不能取消关注自己": "You can't unfollow yourself.",
          "账户或密码错误": "Incorrect email or password.",
          "{{.Name}} 昵称已存在": "Username '{{.Name}}' is taken.",
          "验证码已过期": "Code expired.",
          "发送验证码频率过高": "Too many code requests. Try again soon.",
          "验证码错误": "Wrong code.",
          "当前用户不是 VIP": "This feature requires Premium.",
          "验证码已重新发送，请查收。": "Code sent! Check your inbox.",
          "仅支持中国大陆手机号": "Only mainland China numbers supported.",
          "邮箱": "Email",
          "修改邮箱": "Update Email",
          "邮箱地址": "Email Address",
          "注销成功": "Account deleted successfully",
          "永久删除": "Delete permanently",
          "今天，又见到你了": "Nice to see you again today",
          "明日再见": "See you tomorrow",
          "分享你的想法": "Share your thoughts",
          "告诉我们你对激励功能的想法...":
              "Tell us what you think about the Rewards feature...",
          "提交反馈": "Submit",
          "请输入反馈内容": "Please enter your feedback",
          "感谢您的反馈！": "Thank you for your feedback!",
          "提交失败，请稍后再试": "Failed to submit. Please try again later.",
          "页面加载失败，请重试": "Page loading failed, please try again",
          "刷新一下": "Refresh",
          "加载中...": "Loading...",
          "请检查网络连接后重试": "Please check your network connection and try again",
          "添加激励图片": "Add incentive image",
          "建议提高难度的习惯": "Level Up These Habits",
          "建议降低难度的习惯": "Adjust for Success",
          "进展顺利的习惯": "Well-Balanced Habits",
          "新习惯": "New Habit",
          "编辑习惯": "Edit Habit",
          "习惯名称": "Habit Name",
          "双阶段习惯": "Two-Stage Habit",
          "严格模式": "Strict Mode",
          "打卡后当天不可撤销": "Once checked, can't be unchecked for the day",
          "开启奖励": "Enable Rewards",
          "完成习惯后展示激励": "Add rewards for completion",
          "结束日期": "Set End Date",
          "习惯终止时间": "Set a date when this habit will end",
          "选择结束日期": "Select an end date",
          "此习惯将持续到设定日期": "Your habit will be active until this date",
          "晨跑、阅读": "e.g. Morning Run, Daily Reading",
          "入门小步骤": "Minimum Step",
          "定一个小到不可能失败的第一步":
              "Set a tiny goal impossible to fail, even on bad days",
          "做 1 个俯卧撑": 'e.g. 1 push-up',
          "理想目标": "Stretch Goal",
          "有信心时可以尝试的完整目标": "A bigger goal to aim for once you build confidence",
          "做 10 个俯卧撑": 'e.g. 10 push-ups',
          "记录时刻": "When done",
          "持续时间": "How long",
          "发生次数": "How many",
          " 天": " day left",
          "计时停止": "Timer Paused",
          "时长记录": "Time recorded",
          "是否保存此次计时？": "What would you like to do with this session?",
          "放弃": "Discard",
          "隐私保护": "Privacy Mode",
          "使用通用名称代替习惯真实内容（限时免费）":
              "Replace habit names with generic ones (Limited-time free)",
          "显示模式": "Display Mode",
          "自定义名称": "Custom Name",
          "输入显示名称": "Enter display name",
          '显示为"日常任务"': 'Show as "Daily Task"',
          '星号掩码': "Mask with Asterisks",
          "剩余 ": "",
          "请输入习惯名称": "Please enter a habit name",
          "请输入阶段内容": "Please enter Stage",
          "请选择结束日期": "Please select end date",
          "请输入隐私保护中的显示名称": "Please enter display name",
          "请修复以下错误：": "Please fix the following errors:",
          "创建并管理你的习惯": "Create and manage your habits",
          "设置完成习惯的激励": "Add rewards for completion",
          "统计": "Stats",
          "习惯类型创建后不可修改": "Habit type cannot be changed once created",
          "选择阶段": "Select Stage",
          "设置隐私习惯密码锁": "Set a privacy password lock",

          // 习惯列表页面
          "养成中习惯": "Building Habits",
          "休息中习惯": "On Break Habits",
          "已养成习惯": "Formed Habits",
          "加载习惯中...": "Loading your habits...",
          "暂无习惯": "No habits yet",
          "开始创建你的第一个习惯，养成更好的生活方式":
              "Start building better habits by creating your first habit",
          "创建我的第一个习惯": "Create my first habit",
          "暂无养成中习惯": "No habits in progress",
          "你目前没有养成中的习惯": "Ready to start building your first habit?",
          "暂无休息中习惯": "No habits on break",
          "你目前没有休息中的习惯": "All your habits are staying active!",
          "暂无已养成习惯": "No habits formed yet",
          "你目前没有已养成的习惯": "Your first habit milestone awaits!",
          "未找到习惯": "No habits found",
          "尝试使用其他筛选条件": "Try a different filter",
          "查看所有习惯": "View all habits",
          "天连续打卡": "day streak",
          "我的习惯": "My Habits",
          "添加": "Add New",
          "天打卡": "days maintained",
          "养成中": "Building",
          "休息中": "On Break",
          "已养成": "Formed",
          "太棒啦！所有习惯都已完成": "Great job! All today's habits complete",

          // 习惯操作卡片
          "此习惯已结束": "This habit has been ended.",
          "此习惯当前已暂停": "This habit is currently paused.",
          "删除习惯": "Delete Habit",
          "确定要删除这个习惯吗？此操作无法撤销。":
              "Are you sure you want to delete this habit? This action cannot be undone.",
          "结束习惯": "End Habit",
          "确定要结束这个习惯吗？您仍然可以查看它，但它将被标记为已完成。":
              "Are you sure you want to end this habit? You can still view it, but it will be marked as completed.",
          "数据加载中，请稍后再试": "Loading data, please try again later",
          "删除成功": "Successfully deleted",
          "习惯已标记为完成": "Habit has been marked as completed",
          "恢复": "Resume",
          "不允许操作": "No allow punch",
          "该习惯已完成": "This habit is ended.",
          "该习惯当前已暂停": "This habit is currently paused.",
          "取消打卡": "Undo",
          "打卡进度": "Record your progress",

          // 习惯详情页面 - 时间轴
          "暂无活动记录": "No activities yet",
          "开始记录你的活动吧": "Start tracking your habit to see activity here",
          "记录活动": "Track Activity",
          "活动": "Activity",

          // 随记对话框相关
          "添加随记": "Add Note",
          "编辑随记": "Edit Note",
          "在这里输入内容...": "Add your notes here...",
          "内容为空": "Note is empty",
          "请输入随记内容": "Please add some content to your note",
          "AM": "AM",
          "PM": "PM",

          // 日期和时间
          "周一": "Mon",
          "周二": "Tue",
          "周三": "Wed",
          "周四": "Thu",
          "周五": "Fri",
          "周六": "Sat",
          "周日": "Sun",
          "1月": "Jan",
          "2月": "Feb",
          "3月": "Mar",
          "4月": "Apr",
          "5月": "May",
          "6月": "Jun",
          "7月": "Jul",
          "8月": "Aug",
          "9月": "Sep",
          "10月": "Oct",
          "11月": "Nov",
          "12月": "Dec",

          // 新增翻译
          "功能预览": 'Feature Preview',
          "查看预览": 'View Preview',
          "查看功能": 'View Features',
          "双阶段习惯系统": 'Two-Stage Habit System',
          '独创"最小完成+理想完成"双目标模式，工作再忙也不断链，让坚持变得轻松自然':
              'Smart two-tier goal design: achieve minimum targets on busy days, ideal targets when possible - keeping you consistent without the pressure',
          "隐私习惯保护": 'Privacy Habit Protection',
          "对于敏感习惯提供完全的隐私保护，确保您的个人习惯信息安全并保持隐私":
              'Complete privacy protection for sensitive habits, ensuring your personal habit information is secure and private.',
          "直观进度追踪": 'Visual Progress Tracking',
          "清晰的视觉化数据展示您的一致性和习惯进度，帮助构建持续动力":
              'Clear visual insights showing your consistency and progress over time to build motivation.',

          // 用户页面翻译
          "每日提示": 'Daily Tip',
          "今日锦囊": 'Daily Tip',
          "成就与目标": 'Achievements & Goals',
          "初学者": 'Starter',
          "连续一周": 'Week Streak',
          "习惯大师": 'Habit Pro',
          "早起鸟": 'Early Bird',
          "持之以恒": 'Consistent',
          "专业版": 'Pro',
          "设置与偏好": 'Settings & Preferences',
          "账户": 'Account',
          "管理您的账户设置": 'Manage your account settings',
          "帮助与信息": 'Help & Information',
          "帮助中心": 'Help Center',
          "获取任何问题的帮助": 'Get help with any issues',
          "关于": 'About',
          "隐私政策和使用条款": 'Privacy Policy & Terms',
          "© 2024 保留所有权利": '© 2024 All Rights Reserved',
          "习惯养成师": 'Habit Builder',
          "匿名": "user_none",
          "统计数据": "Your Statistics",
          "所有习惯": "All Habits",
          "培养中": "On Track",
          "达成": "Achievements",

          // 发现页面翻译
          "精选微习惯模板": 'Featured Micro-Habit Templates',
          "快速导航": 'Quick Navigation',
          "微习惯指南": 'Micro-Habit Guides',
          "阅读更多": 'Read more',
          "分钟阅读": 'min read',

          // 帮助与支持页面
          "帮助与支持": 'Help & Support',
          "常见问题、联系客服": 'FAQ & Contact Support',
          "清除跳过版本 (调试)": 'Clear Skipped Version (Debug)',
          "重置版本跳过记录": 'Reset version skip records',
          "联系客服": 'Contact Support',
          "24 小时回复": '24hr response',
          "需要帮助？通过以下渠道联系我们":
              'Need help? Reach out through one of these channels',
          "Twitter / X": 'Twitter / X',
          "@Nil2wl • 通常 2 小时内回复": '@Nil2wl • Typically replies within 2 hours',
          "邮件支持": 'Email Support',
          "<EMAIL> • 24 小时内回复":
              '<EMAIL> • Response within 24 hours',
          "发送反馈": 'Send Feedback',
          "帮助我们改进您的使用体验": 'Help us improve your experience',
          "您的反馈": 'Your feedback',
          "添加截图": 'Add screenshot',
          "截图": 'Screenshots',
          "您的邮箱（可选）": 'Your email (optional)',
          "<EMAIL>": '<EMAIL>',
          "您最多可以上传": 'You can only upload up to',
          "张截图": "screenshots.",
          "图片超过5MB大小限制，请选择较小的文件":
              'The image exceeds the 5MB size limit. Please select a smaller file.',
          "请选择图片文件(PNG, JPEG等)": 'Please select an image file (PNG, JPEG, etc)',
          "选择图片时出错，请重试":
              'Error occurred when selecting the image. Please try again.',
          "无法打开Twitter/X": 'Could not open X',
          "无法打开小红书": 'Could not open Xiaohongshu',
          "请重新登录": 'Please login again',
          "我们只会用它来回复您的反馈": 'We\'ll only use this to respond to your feedback',
          "发送中...": 'Sending...',
          "接下来会发生什么？": 'What happens next?',
          "我们的团队会在48小时内审核所有反馈。如果您提供了邮箱，我们会在需要更多信息时联系您。":
              'Our team reviews all feedback within 48 hours. If you\'ve provided an email, we\'ll reach out if we need more information.',

          // 反馈占位文本
          "请在此提供您的反馈...": 'Please provide your feedback here...',

          // 邮件对话框
          "无法打开邮件客户端": 'Could not open email client',
          "请手动发送邮件至：": 'Please send an email manually to:',
          "复制邮箱地址": 'Copy Email Address',
          "关闭": 'Close',
          "邮箱地址已复制到剪贴板": 'Email address copied to clipboard',

          // 表单提交相关
          "错误": 'Error',
          "请提供一些反馈信息": 'Please provide some feedback',
          "提交前请先修复截图错误": 'Please fix screenshot errors before submitting',
          "反馈发送成功。感谢您帮助我们改进！":
              'High five! 🙏 Your feedback helps us get better!',
          "出错了。请稍后再试。": 'Oops! Something hiccupped. Try again? 🤷‍♀️',

          // 反馈类型
          "建议": 'Suggestion',
          "报告问题": 'Report a bug',
          "功能请求": 'Feature request',

          // 科学依据弹窗相关
          "为什么习惯有效": 'Why This Habit Works',
          "科学依据": 'Scientific Insight',
          "成功率": 'Success Rate',
          " 的用户成功坚持这个习惯至少":
              '% of users successfully maintained this habit for at least',
          "天。": "days.",

          // Current Challenge 区域翻译
          "当前挑战": "Current Challenge",
          "还剩": "Days left:",
          "剩余": "left",
          "完成进度": "Progress",

          // 推荐评分组件翻译
          "Life Habit帮助你养成好习惯了吗？":
              "Is Life Habit helping you crush those goals? ",
          "点击星星评分": "Tap those stars! ",
          "愿意在App Store给我们评价吗？": "Mind dropping a review on the App Store? ",
          "能告诉我们如何改进吗？": "What would make this even better? 🤔",
          "您的建议...": "Your suggestions...",
          "暂不": "Not now",
          "跳过": "Skip",
          "您的反馈已提交！": "Got it! Thanks! 📝",
          "不推荐": "Not likely",
          "十分推荐": "Very likely",
          "感谢您的好评！": "You're awesome! Thanks! 🙌",
          "您的支持是我们前进的动力！愿意在App Store给我们评价吗？":
              "You rock! 🎸 Mind sharing the love on the App Store?",
          "我们很想了解如何改进，能告诉我们您的想法吗？": "Spill the tea! ☕ What could we do better?",
          "暂不评价": "Not now",
          "去评价": "Let's do it! ",
          "请告诉我们您的建议或遇到的问题...":
              "Please tell us your suggestions or any issues you've encountered...",
          "提示": "Notice",
          "请输入您的反馈内容": "Please enter your feedback",
          "感谢": "Thank you",
          "您的反馈已提交，我们会认真考虑您的建议！":
              "Message received loud and clear! 📢 We're on it!",
          "提交失败，请稍后重试": "Whoops! Let's try that again 🔄",
          "无法打开App Store": "Unable to open App Store",
          "反馈": "Feedback",
          "无法打开反馈页面": "Unable to open feedback page",

          // 新增缺失的翻译
          "当前平台不支持跳转到应用商店": "App store not supported on this platform",
          "无法打开应用商店": "Unable to open app store",
          "无法打开Google Play": "Unable to open Google Play",
          "请在移动设备上使用此功能": "Please use this feature on a mobile device",
          "去App Store评价": "Hit up the App Store! ",
          "去Google Play评价": "Check us out on Google Play! ",
          "给个小心心呗？": "Mind giving us some love? ",
          "去Google Play夸夸我们？": "Fancy rating us on Google Play? ",
          "去 App Store 夸夸我们？": "Fancy rating us on App Store? ",
          "新习惯开始养成啦！": "Boom! New habit unlocked! ",
          "习惯升级成功！": "Habit leveled up! ",

          // 错误提示翻译
          "网络不给力，请稍后重试": "Network unavailable, please try again",
          "未知错误，请重试": "Unknown error, please try again",
          "请求超时，请重试": "Request timeout, please try again",
          "网络连接不可用": "Network connection unavailable",
          "服务器错误": "Server error",
          
          // 网络请求错误翻译
          "连接超时，请检查网络": "Connection timeout, please check your network",
          "响应超时，请重试": "Response timeout, please try again",
          "网络连接失败，请检查网络设置": "Connection failed, please check network settings",
          "请求已取消": "Request cancelled",
          "网络错误，请稍后重试": "Network error, please try again later",
          
          // HTTP状态码错误翻译
          "参数错误，请检查后重试": "Invalid parameters, please check and try again",
          "权限不足": "Insufficient permissions",
          "请求的资源不存在": "Requested resource not found",
          "服务器内部错误": "Internal server error",
          "网络连接失败": "Network connection failed",
          "服务器错误，请稍后重试": "Server error, please try again later",
          "请求错误，请检查后重试": "Request error, please check and try again",
          "网络繁忙，请稍后重试": "Network busy, please try again later",
          
          // 每日提示刷新相关
          "刷新成功": "Refreshed",
          "今天的刷新次数已用完，明天再来获取新的锦囊吧～": "Daily refresh limit reached. Come back tomorrow for more tips!",

          // 搭子功能相关翻译
          "邀请": "Invite",
          "成为你的习惯搭子": "to be your habit buddy",
          "邀请消息": "Invitation Message",
          "写点什么来介绍自己吧...": "Write something to introduce yourself...",
          "快速选择": "Quick Select",
          "发送邀请": "Send Invitation",
          "希望我们能一起养成好习惯！": "Hope we can build good habits together!",
          "一起坚持，互相监督吧～": "Let's persist together and supervise each other~",
          "让我们成为习惯搭子，共同进步！": "Let's become habit buddies and progress together!",
          "想和你一起培养好习惯，加油！": "Want to develop good habits with you, let's go!",
          "点击填充": "Tap to fill",

          // site_motivation_view 相关翻译
          "每一次坚持，都值得被看见": "Every step forward deserves recognition",
          "用专属于你的图片，见证每一个成长瞬间": "Celebrate your progress with personal rewards",
          "你的专属激励": "Your Personal Reward",
          "上传一张有意义的图片": "Upload a meaningful image",
          "点击添加激励图片": "Tap to add your reward image",
          "选择一张对你有特殊意义的图片": "Choose something that motivates you",
          "激励的意义": "How it works",
          "见证每一次成长": "Witness every growth",
          "当你完成了当天所有的习惯，那张专属于你的图片会悄然出现，仿佛在说：\"我看见了你的努力，你真的很棒！\"": "Complete all your daily habits to unlock your personal reward image and feel the satisfaction of achievement",
          "情感的力量": "Emotional connection",
          "可以是家人的笑脸、梦想的目标，或是任何让你心动的瞬间。这些画面承载着你的情感，陪伴你走过每一个坚持的日子": "Choose family photos, goal images, or anything that touches your heart and motivates your journey",
          "温馨提示": "Usage notes",
          "目前支持图片激励，每日完成习惯后仅展示一次。更多暖心功能正在路上，敬请期待": "Currently supports images, shows once per day after completing habits. More features coming soon",
          "🎉 太棒了！": "🎉 Amazing!",
          "这就是激励你前进的力量": "This is the power that motivates you",

          // 版本更新相关翻译
          "检查更新": 'Check for Updates',
          "获取最新版本": 'Get the latest version',
          "发现新版本": 'New version available',
          "(必须更新)": ' (Required)',
          "当前已是最新版本": 'You are using the latest version',
          "检查更新失败，请稍后重试": 'Failed to check for updates, please try again later',
          "最新版本：": 'Latest Version: ',
          "安装包大小：": 'Package Size: ',
          "稍后": 'Later',
          "立即更新": 'Update Now',
          "无法打开更新链接": 'Unable to open update link',
          "暂无可用的更新链接": 'No update link available',
          "更新失败，请稍后重试": 'Update failed, please try again later',
          "正在下载更新包，请稍候...": 'Downloading update package, please wait...',
          "正在跳转到Google Play...": 'Redirecting to Google Play...',
          "检查中...": 'Checking...',
        }
      };
}
