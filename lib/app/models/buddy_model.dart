/// 搭子用户模型
class BuddyUser {
  final int? id;
  final String nickname;
  final String? avatarUrl;
  final String? description;
  final int? habitCount; // 习惯数量
  final bool isOnline; // 是否在线
  final DateTime? lastActiveTime; // 最后活跃时间
  final BuddyStatus status; // 搭子状态
  final String uid;

  BuddyUser({
    this.id,
    required this.nickname,
    required this.uid,
    this.avatarUrl,
    this.description,
    this.habitCount,
    this.isOnline = false,
    this.lastActiveTime,
    this.status = BuddyStatus.none,
  });

  factory BuddyUser.fromJson(Map<String, dynamic> json) {
    return BuddyUser(
      id: json['id'] is String ? int.tryParse(json['id']) : json['id'],
      nickname: json['nickname']?.toString() ?? '',
      uid: json['uid']?.toString() ?? '',
      avatarUrl: json['avatarUrl']?.toString(),
      description: json['description']?.toString(),
      habitCount: json['habitCount'] is String ? int.tryParse(json['habitCount']) : json['habitCount'],
      isOnline: json['isOnline'] ?? false,
      lastActiveTime: json['lastActiveTime'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['lastActiveTime'])
          : null,
      status: BuddyStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => BuddyStatus.none,
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'nickname': nickname,
      'avatarUrl': avatarUrl,
      'description': description,
      'habitCount': habitCount,
      'isOnline': isOnline,
      'lastActiveTime': lastActiveTime?.millisecondsSinceEpoch,
      'status': status.name,
      'uid': uid,
    };
  }

  BuddyUser copyWith({
    int? id,
    String? nickname,
    String? uid,
    String? avatarUrl,
    String? description,
    int? habitCount,
    bool? isOnline,
    DateTime? lastActiveTime,
    BuddyStatus? status,
  }) {
    return BuddyUser(
      id: id ?? this.id,
      nickname: nickname ?? this.nickname,
      uid: uid ?? this.uid,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      description: description ?? this.description,
      habitCount: habitCount ?? this.habitCount,
      isOnline: isOnline ?? this.isOnline,
      lastActiveTime: lastActiveTime ?? this.lastActiveTime,
      status: status ?? this.status,
    );
  }
}

/// 搭子状态枚举
enum BuddyStatus {
  none, // 无关系，可以添加
  pending, // 邀请待确认
  accepted, // 已接受，成为搭子
  rejected, // 已拒绝
  blocked, // 已屏蔽
  limitReached, // 搭子数量已满
}

/// 搭子数量限制状态
enum BuddyLimitStatus {
  normal, // 正常，可以添加
  warning, // 警告，接近上限
  limitReached, // 已达上限
}

/// 搭子搜索结果模型
class BuddySearchResult {
  final List<BuddyUser> users;
  final int total;
  final bool canAddBuddy; // 是否还能添加搭子
  final String? statusMessage; // 状态提示信息

  BuddySearchResult({
    required this.users,
    required this.total,
    this.canAddBuddy = true,
    this.statusMessage,
  });

  factory BuddySearchResult.fromJson(Map<String, dynamic> json) {
    return BuddySearchResult(
      users: (json['users'] as List<dynamic>?)
              ?.map((e) => BuddyUser.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      total: json['total'] ?? 0,
      canAddBuddy: json['canAddBuddy'] ?? true,
      statusMessage: json['statusMessage'],
    );
  }



  Map<String, dynamic> toJson() {
    return {
      'users': users.map((e) => e.toJson()).toList(),
      'total': total,
      'canAddBuddy': canAddBuddy,
      'statusMessage': statusMessage,
    };
  }

  // 辅助方法，用于其他模型
  static BuddyLimitStatus _parseBuddyLimitStatus(String? status) {
    switch (status) {
      case 'warning':
        return BuddyLimitStatus.warning;
      case 'limit_reached':
        return BuddyLimitStatus.limitReached;
      default:
        return BuddyLimitStatus.normal;
    }
  }

  static String _buddyStatusToString(BuddyLimitStatus status) {
    switch (status) {
      case BuddyLimitStatus.warning:
        return 'warning';
      case BuddyLimitStatus.limitReached:
        return 'limit_reached';
      default:
        return 'normal';
    }
  }
}

/// 搭子列表结果模型
class BuddyListResult {
  final List<BuddyRelationship> buddies;
  final bool canAddBuddy; // 是否还能添加搭子
  final BuddyLimitStatus buddyStatus; // 搭子数量状态
  final String? statusMessage; // 状态提示信息

  BuddyListResult({
    required this.buddies,
    this.canAddBuddy = true,
    this.buddyStatus = BuddyLimitStatus.normal,
    this.statusMessage,
  });

  factory BuddyListResult.fromJson(Map<String, dynamic> json) {
    return BuddyListResult(
      buddies: (json['data'] as List<dynamic>?)
              ?.map(
                  (e) => BuddyRelationship.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      canAddBuddy: json['canAddBuddy'] ?? true,
      buddyStatus:
          BuddySearchResult._parseBuddyLimitStatus(json['buddyStatus']),
      statusMessage: json['statusMessage'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'data': buddies.map((e) => e.toJson()).toList(),
      'canAddBuddy': canAddBuddy,
      'buddyStatus': BuddySearchResult._buddyStatusToString(buddyStatus),
      'statusMessage': statusMessage,
    };
  }
}

/// 搭子邀请模型
class BuddyInvitation {
  final String id;
  final String fromUserId;
  final String toUserId;
  final BuddyUser? fromUser;
  final BuddyUser? toUser;
  final String? message;
  final BuddyInvitationStatus status;
  final DateTime createdAt;
  final DateTime? updatedAt;

  BuddyInvitation({
    required this.id,
    required this.fromUserId,
    required this.toUserId,
    this.fromUser,
    this.toUser,
    this.message,
    required this.status,
    required this.createdAt,
    this.updatedAt,
  });

  factory BuddyInvitation.fromJson(Map<String, dynamic> json) {
    return BuddyInvitation(
      id: json['id']?.toString() ?? '',
      fromUserId: json['fromUserId']?.toString() ?? '',
      toUserId: json['toUserId']?.toString() ?? '',
      fromUser: json['fromUser'] != null
          ? BuddyUser.fromJson(json['fromUser'])
          : null,
      toUser:
          json['toUser'] != null ? BuddyUser.fromJson(json['toUser']) : null,
      message: json['message'],
      status: BuddyInvitationStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => BuddyInvitationStatus.pending,
      ),
      createdAt: DateTime.fromMillisecondsSinceEpoch(json['createdAt']),
      updatedAt: json['updatedAt'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['updatedAt'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'fromUserId': fromUserId,
      'toUserId': toUserId,
      'fromUser': fromUser?.toJson(),
      'toUser': toUser?.toJson(),
      'message': message,
      'status': status.name,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt?.millisecondsSinceEpoch,
    };
  }
}

/// 搭子邀请状态枚举
enum BuddyInvitationStatus {
  pending, // 待确认
  accepted, // 已接受
  rejected, // 已拒绝
  cancelled, // 已取消
}

/// 搭子关系模型
class BuddyRelationship {
  final String id;
  final String userId;
  final String buddyId;
  final BuddyUser? buddy;
  final DateTime createdAt;
  final bool isActive;
  final int? mutualHabits; // 共同习惯数量

  BuddyRelationship({
    required this.id,
    required this.userId,
    required this.buddyId,
    this.buddy,
    required this.createdAt,
    this.isActive = true,
    this.mutualHabits,
  });

  factory BuddyRelationship.fromJson(Map<String, dynamic> json) {
    return BuddyRelationship(
      id: json['id']?.toString() ?? '',
      userId: json['userId']?.toString() ?? '',
      buddyId: json['buddyId']?.toString() ?? '',
      buddy: json['buddy'] != null ? BuddyUser.fromJson(json['buddy']) : null,
      createdAt: DateTime.fromMillisecondsSinceEpoch(json['createdAt']),
      isActive: json['isActive'] ?? true,
      mutualHabits: json['mutualHabits'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'buddyId': buddyId,
      'buddy': buddy?.toJson(),
      'createdAt': createdAt.millisecondsSinceEpoch,
      'isActive': isActive,
      'mutualHabits': mutualHabits,
    };
  }
}
