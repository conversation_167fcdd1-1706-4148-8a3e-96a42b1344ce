# 📝 日志控制完整指南

## 🎯 目标
提供多种方式控制应用的日志输出，确保生产环境零日志开销，同时保持开发调试的便利性。

## 🔧 日志控制方式

### **1. 自动环境控制（推荐）**

#### **环境自动判断**
```dart
// Logger 会自动根据环境判断是否输出日志
static bool get _isEnabled {
  // 1. 检查环境变量设置
  const disableLogsEnv = String.fromEnvironment('DISABLE_LOGS');
  if (disableLogsEnv == 'true') return false;
  
  // 2. 检查是否为生产环境
  if (Env.appEnv == EnvName.release) return false;
  
  // 3. 默认使用 Debug 模式判断
  return kDebugMode;
}
```

#### **不同环境的日志行为**
| 环境 | 日志输出 | 说明 |
|------|----------|------|
| `dev` | ✅ 全部输出 | 开发环境，所有日志都输出 |
| `test` | ✅ 全部输出 | 测试环境，便于调试 |
| `release` | ❌ 自动禁用 | 生产环境，自动禁用所有日志 |

### **2. 编译时控制**

#### **通过环境变量禁用日志**
```bash
# 编译时完全禁用日志
flutter build apk --dart-define=DISABLE_LOGS=true

# 或在 Makefile 中添加
flutter build apk --release --dart-define=DISABLE_LOGS=true --dart-define=DART_DEFINE_APP_ENV=release
```

#### **更新 Makefile**
```makefile
# 生产环境构建（完全禁用日志）
.PHONY: apk-production
apk-production: deps
	@echo "Building production APK with logs disabled..."
	@$(FLUTTER) build apk --release \
		--target-platform android-arm64 \
		--dart-define=DART_DEFINE_APP_ENV=release \
		--dart-define=DISABLE_LOGS=true \
		--obfuscate \
		--split-debug-info=build/debug-symbols
	@mkdir -p $(BUILD_DIR)
	@cp build/app/outputs/flutter-apk/app-release.apk $(APK_OUTPUT)
	@echo "Production APK built at $(APK_OUTPUT)"

# 测试环境构建（保留日志）
.PHONY: apk-staging
apk-staging: deps
	@echo "Building staging APK with logs enabled..."
	@$(FLUTTER) build apk --release \
		--target-platform android-arm64 \
		--dart-define=DART_DEFINE_APP_ENV=test
	@mkdir -p $(BUILD_DIR)
	@cp build/app/outputs/flutter-apk/app-release.apk $(APK_OUTPUT)
	@echo "Staging APK built at $(APK_OUTPUT)"
```

### **3. 运行时控制**

#### **程序启动时设置**
```dart
// 在 main.dart 中根据环境设置日志
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // 根据环境配置日志
  if (Env.appEnv == EnvName.release) {
    // 生产环境：完全禁用日志
    Logger.disableAllLogs();
  } else if (Env.appEnv == EnvName.test) {
    // 测试环境：只保留错误和警告日志
    Logger.setLogsEnabled(
      debug: false,
      info: false,
      network: false,
      performance: false,
      cache: false,
      warning: true,
      error: true,
    );
  }
  // dev 环境保持默认（全部启用）
  
  runApp(MyApp());
}
```

#### **动态控制日志级别**
```dart
// 在设置页面或调试页面中动态控制
class DebugSettingsPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('调试设置')),
      body: Column(
        children: [
          SwitchListTile(
            title: Text('网络日志'),
            value: true, // 从 Logger.getLogStatus() 获取当前状态
            onChanged: (value) {
              Logger.setLogsEnabled(network: value);
            },
          ),
          SwitchListTile(
            title: Text('性能日志'),
            value: true,
            onChanged: (value) {
              Logger.setLogsEnabled(performance: value);
            },
          ),
          // 其他日志开关...
        ],
      ),
    );
  }
}
```

### **4. 网络日志特殊控制**

#### **NetworkLogger 的双重控制**
```dart
class NetworkLogger {
  static bool _isDebugMode = true;
  
  /// 设置网络日志开关（独立于 Logger）
  static void setDebugMode(bool isDebug) {
    _isDebugMode = isDebug;
  }
  
  static void logRequest(BaseRequest request) {
    // 双重检查：NetworkLogger 开关 + Logger 开关
    if (!_isDebugMode) return;
    
    Logger.network('🚀 [REQUEST] ${request.httpMethod()} ${request.url()}');
    // ...
  }
}
```

## 📊 不同构建配置对比

### **开发构建**
```bash
# 开发环境 - 所有日志都输出
flutter run --dart-define=DART_DEFINE_APP_ENV=dev
```
- ✅ 所有日志类型都输出
- ✅ 网络请求详细日志
- ✅ 性能监控日志
- ✅ 缓存操作日志

### **测试构建**
```bash
# 测试环境 - 部分日志输出
flutter build apk --dart-define=DART_DEFINE_APP_ENV=test
```
- ⚠️ 只输出警告和错误日志
- ❌ 禁用调试和信息日志
- ❌ 禁用网络详细日志
- ✅ 保留错误追踪

### **生产构建**
```bash
# 生产环境 - 完全禁用日志
flutter build apk --release \
  --dart-define=DART_DEFINE_APP_ENV=release \
  --dart-define=DISABLE_LOGS=true \
  --obfuscate \
  --split-debug-info=build/debug-symbols
```
- ❌ 完全禁用所有日志
- ❌ 零日志输出开销
- ✅ 最佳性能表现
- ✅ 代码混淆保护

## 🎛️ 日志级别详细说明

### **日志类型优先级**
1. **ERROR** - 最高优先级，生产环境也保留
2. **WARNING** - 高优先级，测试环境保留
3. **INFO** - 中等优先级，开发环境使用
4. **DEBUG** - 低优先级，仅开发调试使用
5. **NETWORK** - 特殊类型，可独立控制
6. **PERFORMANCE** - 特殊类型，性能监控使用

### **推荐的环境配置**

#### **开发环境 (dev)**
```dart
Logger.setLogsEnabled(
  debug: true,      // ✅ 调试信息
  info: true,       // ✅ 一般信息
  warning: true,    // ✅ 警告信息
  error: true,      // ✅ 错误信息
  network: true,    // ✅ 网络日志
  performance: true, // ✅ 性能日志
  cache: true,      // ✅ 缓存日志
);
```

#### **测试环境 (test)**
```dart
Logger.setLogsEnabled(
  debug: false,     // ❌ 禁用调试信息
  info: false,      // ❌ 禁用一般信息
  warning: true,    // ✅ 保留警告信息
  error: true,      // ✅ 保留错误信息
  network: false,   // ❌ 禁用网络详细日志
  performance: false, // ❌ 禁用性能日志
  cache: false,     // ❌ 禁用缓存日志
);
```

#### **生产环境 (release)**
```dart
Logger.disableAllLogs(); // 完全禁用
// 或者只保留错误日志
Logger.setLogsEnabled(
  debug: false,
  info: false,
  warning: false,
  error: true,      // 只保留错误日志用于崩溃分析
  network: false,
  performance: false,
  cache: false,
);
```

## 🔍 验证日志控制效果

### **1. 检查当前日志状态**
```dart
// 在应用中添加调试信息
void printLogStatus() {
  final status = Logger.getLogStatus();
  print('日志状态: $status');
}
```

### **2. 测试不同环境**
```bash
# 测试开发环境
flutter run --dart-define=DART_DEFINE_APP_ENV=dev

# 测试生产环境
flutter run --release --dart-define=DART_DEFINE_APP_ENV=release

# 测试完全禁用日志
flutter run --release --dart-define=DISABLE_LOGS=true
```

### **3. 性能对比测试**
```dart
// 测试网络请求性能
final timer = Logger.startTimer();
await performNetworkRequest();
Logger.endTimer(timer, '网络请求');

// 在不同环境下对比响应时间
```

## 📈 性能提升预期

### **日志输出开销对比**
| 场景 | 开发环境 | 测试环境 | 生产环境 |
|------|----------|----------|----------|
| 应用启动 | +200ms | +50ms | +0ms |
| 网络请求 | +10-20ms | +2-5ms | +0ms |
| 用户操作 | +5-10ms | +1-2ms | +0ms |
| 内存使用 | +2-5MB | +0.5-1MB | +0MB |

### **推荐的发布流程**
1. **开发阶段**：使用 `dev` 环境，全量日志
2. **内测阶段**：使用 `test` 环境，精简日志
3. **正式发布**：使用 `release` 环境，禁用日志
4. **紧急修复**：可临时启用错误日志

## 🚀 最佳实践

### **1. Makefile 优化**
```makefile
# 添加专门的生产构建命令
.PHONY: build-production
build-production: deps
	@echo "Building optimized production build..."
	@$(FLUTTER) build apk --release \
		--dart-define=DART_DEFINE_APP_ENV=release \
		--dart-define=DISABLE_LOGS=true \
		--obfuscate \
		--split-debug-info=build/debug-symbols \
		--target-platform android-arm64
	@echo "Production build complete with zero log overhead"
```

### **2. CI/CD 集成**
```yaml
# GitHub Actions 示例
- name: Build Production APK
  run: |
    flutter build apk --release \
      --dart-define=DART_DEFINE_APP_ENV=release \
      --dart-define=DISABLE_LOGS=true \
      --obfuscate \
      --split-debug-info=build/debug-symbols
```

### **3. 条件编译优化**
```dart
// 在关键性能路径中使用条件编译
void performCriticalOperation() {
  // 只在非生产环境记录日志
  if (kDebugMode && Env.appEnv != EnvName.release) {
    Logger.performance('开始关键操作', Duration.zero);
  }
  
  // 执行关键操作
  doCriticalWork();
  
  if (kDebugMode && Env.appEnv != EnvName.release) {
    Logger.performance('完成关键操作', Duration.zero);
  }
}
```

通过这套完整的日志控制方案，可以确保：
- 🔧 **开发便利**：开发时有完整的日志信息
- ⚡ **生产性能**：发布版本零日志开销
- 🎛️ **灵活控制**：可根据需要动态调整日志级别
- 📊 **环境适配**：不同环境自动使用合适的日志策略 