# 🔥 Firebase Cloud Messaging (FCM) 配置指南

## ✅ **当前配置状态**
- ✅ `android/app/google-services.json` 已存在
- ✅ `ios/Runner/GoogleService-Info.plist` 已存在
- ✅ `firebase_options.dart` 已生成
- ✅ 基础 FCM 代码已实现

## 🛠️ **仍需完成的配置**

### **1. Android 配置检查**

#### **1.1 检查 `android/build.gradle`**
```gradle
buildscript {
    dependencies {
        // 确保版本 >= 4.3.0
        classpath 'com.google.gms:google-services:4.4.0'
    }
}
```

#### **1.2 检查 `android/app/build.gradle`**
```gradle
android {
    compileSdkVersion 34
    
    defaultConfig {
        minSdkVersion 21  // FCM 最低要求
        targetSdkVersion 34
    }
}

dependencies {
    implementation platform('com.google.firebase:firebase-bom:32.7.0')
    implementation 'com.google.firebase:firebase-messaging'
}

// 文件末尾
apply plugin: 'com.google.gms.google-services'
```

#### **1.3 检查 `android/app/src/main/AndroidManifest.xml`**
```xml
<manifest xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- 网络权限 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.VIBRATE" />
    
    <!-- Android 13+ 通知权限 -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>
    
    <application android:name="${applicationName}">
        
        <!-- FCM 服务 -->
        <service
            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>
        
        <!-- 默认通知图标和颜色 -->
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@mipmap/ic_launcher" />
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_color"
            android:resource="@color/notification_color" />
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_channel_id"
            android:value="habit_reminders" />
            
    </application>
</manifest>
```

#### **1.4 创建通知颜色资源 `android/app/src/main/res/values/colors.xml`**
```xml
<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="notification_color">#4285F4</color>
</resources>
```

### **2. iOS 配置检查**

#### **2.1 检查 `ios/Runner/Info.plist`**
```xml
<dict>
    <!-- Firebase 配置 -->
    <key>FirebaseAppDelegateProxyEnabled</key>
    <false/>
    
    <!-- 后台模式 -->
    <key>UIBackgroundModes</key>
    <array>
        <string>background-processing</string>
        <string>remote-notification</string>
    </array>
</dict>
```

#### **2.2 检查 `ios/Runner/Runner.entitlements`**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <!-- 推送通知权限 -->
    <key>aps-environment</key>
    <string>development</string> <!-- 或 production -->
</dict>
</plist>
```

#### **2.3 更新 `ios/Runner/AppDelegate.swift`**
```swift
import UIKit
import Flutter
import firebase_messaging

@UIApplicationMain
@objc class AppDelegate: FlutterAppDelegate {
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    
    // 注册推送通知类型
    if #available(iOS 10.0, *) {
      UNUserNotificationCenter.current().delegate = self as UNUserNotificationCenterDelegate
    }
    
    GeneratedPluginRegistrant.register(with: self)
    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }
  
  // 处理前台通知显示
  override func userNotificationCenter(
    _ center: UNUserNotificationCenter,
    willPresent notification: UNNotification,
    withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void
  ) {
    completionHandler([[.banner, .sound]])
  }
}
```

### **3. 服务依赖注册**

#### **3.1 在 `lib/main.dart` 中注册服务**
```dart
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // 注册服务（按依赖顺序）
  Get.put(DeviceInfoService());
  Get.put(PushNavigationService());
  Get.put(HabitReminderService());
  Get.put(NotificationService());
  
  runApp(MyApp());
}
```

#### **3.2 或在依赖注入文件中注册**
```dart
// lib/app/bindings/initial_binding.dart
class InitialBinding extends Bindings {
  @override
  void dependencies() {
    // 核心服务
    Get.lazyPut(() => DeviceInfoService());
    Get.lazyPut(() => PushNavigationService());
    Get.lazyPut(() => HabitReminderService());
    Get.lazyPut(() => NotificationService());
  }
}
```

### **4. pubspec.yaml 依赖检查**
```yaml
dependencies:
  flutter:
    sdk: flutter
  
  # Firebase 套件
  firebase_core: ^2.24.2
  firebase_messaging: ^14.7.10
  
  # 本地通知
  flutter_local_notifications: ^16.3.0
  timezone: ^0.9.2
  
  # 权限管理
  permission_handler: ^11.1.0
  
  # 状态管理
  get: ^4.6.6
```

## 🚀 **测试步骤**

### **1. 基础连接测试**
```bash
# 清理并重新构建
flutter clean
flutter pub get

# Android 测试
flutter run --debug
# 检查日志: "✅ Firebase Messaging 初始化成功"

# iOS 测试  
flutter run --debug
# 检查日志: "🎯 FCM Token: [token]"
```

### **2. 推送权限测试**
```dart
// 在应用中测试
final hasPermission = await NotificationService.instance.isPermissionGranted;
print('推送权限状态: $hasPermission');
```

### **3. Token 获取测试**
```dart
final token = NotificationService.instance.fcmToken;
print('FCM Token: $token');
```

### **4. 本地通知测试**
```dart
await HabitReminderService.instance.showInstantReminder(
  title: '测试通知',
  body: '这是一个测试消息',
);
```

### **5. 习惯提醒测试**
```dart
await HabitReminderService.instance.scheduleHabitReminder(
  habitId: 1,
  habitName: '喝水',
  scheduledTime: DateTime.now().add(Duration(minutes: 1)),
);
```

## 🔧 **故障排除**

### **常见问题**

#### **1. FCM Token 为 null**
- 检查网络连接
- 检查 Firebase 配置文件
- 检查是否在中国大陆（使用 VPN 测试）

#### **2. 通知不显示**
- 检查权限：`Permission.notification.status`
- 检查通知渠道设置
- 检查设备是否开启勿扰模式

#### **3. iOS 推送不工作**
- 检查 APNs 证书配置
- 检查 Bundle ID 是否匹配
- 检查 entitlements 文件

#### **4. Android 后台不接收推送**
- 检查电池优化设置
- 检查自启动权限
- 添加 `android:exported="false"` 到服务

## 📊 **监控和调试**

### **日志关键字**
```bash
# Flutter 日志
flutter logs | grep -E "(FCM|推送|通知|Firebase)"

# Android 日志  
adb logcat | grep -E "(FirebaseMessaging|FCM)"

# iOS 日志（Xcode Console）
搜索: "firebase", "notification", "APNS"
```

### **测试工具**
- Firebase Console → Cloud Messaging → 发送测试消息
- `curl` 命令测试 FCM API
- 设备的系统通知设置

## 🌍 **区域化支持**

### **中国大陆用户优化**
```dart
// 自动检测并切换到备用推送方案
if (await NotificationService.instance.isInChina) {
  // 使用极光推送或其他国内推送服务
  // 或仅使用本地通知
}
```

### **海外用户标准配置**
- 正常使用 FCM
- 支持 iOS APNs
- 完整的推送功能

---

## ✅ **配置完成检查清单**

- [ ] Android `google-services.json` 已配置
- [ ] iOS `GoogleService-Info.plist` 已配置  
- [ ] `firebase_options.dart` 已生成
- [ ] Android Manifest 权限已添加
- [ ] iOS entitlements 已配置
- [ ] 服务依赖已注册
- [ ] 推送权限测试通过
- [ ] Token 获取测试通过
- [ ] 本地通知测试通过
- [ ] 习惯提醒功能测试通过

完成所有配置后，你的应用将具备：
- 🔥 完整的 FCM 推送功能
- 📱 本地通知和提醒
- 🧭 智能导航处理  
- 🌍 区域化推送策略
- 🔧 完善的错误处理 