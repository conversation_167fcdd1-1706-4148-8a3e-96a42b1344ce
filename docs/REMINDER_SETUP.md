# 习惯提醒功能集成指南

## 概述
本指南将帮助您在生活习惯App中完整集成推送提醒功能，支持本地通知和Firebase云消息推送。

## 功能特性
- ✅ 本地定时提醒通知
- ✅ Firebase云消息推送
- ✅ 多平台支持（iOS/Android）
- ✅ 智能提醒时间推荐
- ✅ 美观的UI界面
- ✅ 权限管理和错误处理
- ✅ 时区支持

## 依赖包列表

### 必需依赖
```yaml
dependencies:
  # Firebase 相关
  firebase_core: ^2.24.2
  firebase_messaging: ^14.7.10
  
  # 本地通知
  flutter_local_notifications: ^16.3.0
  
  # 时区支持
  timezone: ^0.9.2
  
  # 权限管理
  permission_handler: ^11.0.1
  
  # UI组件
  flutter_advanced_switch: ^3.1.0
  
  # 状态管理（如果项目中未包含）
  get: ^4.6.6

dev_dependencies:
  # 代码生成（如果需要）
  build_runner: ^2.4.7
```

## 1. Firebase 配置

### 1.1 创建 Firebase 项目
1. 访问 [Firebase Console](https://console.firebase.google.com/)
2. 创建新项目或选择现有项目
3. 启用 Cloud Messaging 服务

### 1.2 Android 配置

#### 下载配置文件
- 在 Firebase Console 中添加 Android 应用
- 下载 `google-services.json` 文件
- 将文件放置到 `android/app/` 目录下

#### 修改 build.gradle 文件

**android/build.gradle:**
```gradle
buildscript {
    dependencies {
        // 添加 Google Services 插件
        classpath 'com.google.gms:google-services:4.3.15'
    }
}
```

**android/app/build.gradle:**
```gradle
plugins {
    id 'com.android.application'
    id 'kotlin-android'
    id 'dev.flutter.flutter-gradle-plugin'
    // 添加这行
    id 'com.google.gms.google-services'
}

dependencies {
    // Firebase BOM
    implementation platform('com.google.firebase:firebase-bom:32.7.0')
    implementation 'com.google.firebase:firebase-messaging'
}
```

#### 权限配置

**android/app/src/main/AndroidManifest.xml:**
```xml
<manifest xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 网络权限 -->
    <uses-permission android:name="android.permission.INTERNET" />
    
    <!-- 通知权限 -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.VIBRATE" />
    
    <!-- 精确闹钟权限（Android 12+） -->
    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
    <uses-permission android:name="android.permission.USE_EXACT_ALARM" />

    <application>
        <!-- Firebase Messaging Service -->
        <service
            android:name=".java.MyFirebaseMessagingService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>
        
        <!-- 通知图标（可选） -->
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@drawable/ic_notification" />
        
        <!-- 通知颜色（可选） -->
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_color"
            android:resource="@color/colorAccent" />
    </application>
</manifest>
```

### 1.3 iOS 配置

#### 下载配置文件
- 在 Firebase Console 中添加 iOS 应用
- 下载 `GoogleService-Info.plist` 文件
- 将文件添加到 `ios/Runner/` 目录，并确保在 Xcode 中正确引用

#### 权限配置

**ios/Runner/Info.plist:**
```xml
<dict>
    <!-- 其他配置 -->
    
    <!-- 后台模式 -->
    <key>UIBackgroundModes</key>
    <array>
        <string>fetch</string>
        <string>remote-notification</string>
    </array>
    
    <!-- 通知权限描述 -->
    <key>NSUserNotificationUsageDescription</key>
    <string>我们需要发送通知来提醒您完成习惯打卡</string>
</dict>
```

## 2. 项目集成

### 2.1 初始化服务

**lib/main.dart:**
```dart
import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:get/get.dart';
import 'package:timezone/data/latest.dart' as tz;
import 'app/services/notification_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // 1. 初始化时区数据
  tz.initializeTimeZones();
  
  // 2. 初始化 Firebase
  await Firebase.initializeApp();
  
  // 3. 注册推送服务
  Get.put(NotificationService(), permanent: true);
  
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      title: 'Life Habit App',
      home: HomePage(),
      // 其他配置...
    );
  }
}
```

### 2.2 在需要的页面中使用

**创建习惯页面示例:**
```dart
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../services/notification_service.dart';
import '../widgets/habit_reminder_widget.dart';

class HabitCreatePage extends StatefulWidget {
  @override
  _HabitCreatePageState createState() => _HabitCreatePageState();
}

class _HabitCreatePageState extends State<HabitCreatePage> {
  String habitName = '';
  HabitReminder? currentReminder;
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          // 习惯名称输入
          TextField(
            decoration: InputDecoration(labelText: '习惯名称'),
            onChanged: (value) => setState(() => habitName = value),
          ),
          
          // 提醒设置组件
          HabitReminderWidget(
            habitName: habitName.isNotEmpty ? habitName : '新习惯',
            initialReminder: currentReminder,
            onReminderSet: (reminder) {
              setState(() => currentReminder = reminder);
            },
            onReminderRemoved: () {
              setState(() => currentReminder = null);
            },
          ),
          
          // 创建按钮
          ElevatedButton(
            onPressed: _createHabit,
            child: Text('创建习惯'),
          ),
        ],
      ),
    );
  }
  
  void _createHabit() async {
    // 1. 创建习惯
    final habitId = await createHabitAPI(habitName);
    
    // 2. 设置提醒
    if (currentReminder != null) {
      await NotificationService.instance.scheduleHabitReminder(
        habitId: habitId,
        habitName: habitName,
        scheduledTime: DateTime.now().copyWith(
          hour: currentReminder!.reminderTime.hour,
          minute: currentReminder!.reminderTime.minute,
        ),
        isRepeating: true,
      );
    }
    
    Get.back();
  }
}
```

## 3. 测试验证

### 3.1 权限检查
```dart
// 检查通知权限
final hasPermission = await NotificationService.instance.isPermissionGranted;
print('通知权限: $hasPermission');

// 获取FCM Token
final fcmToken = NotificationService.instance.fcmToken;
print('FCM Token: $fcmToken');
```

### 3.2 测试本地通知
```dart
// 发送测试通知
await NotificationService.instance._showLocalNotification(
  title: '测试通知',
  body: '这是一条测试消息',
);
```

### 3.3 测试定时提醒
```dart
// 设置5分钟后的提醒
await NotificationService.instance.scheduleHabitReminder(
  habitId: 999,
  habitName: '测试习惯',
  scheduledTime: DateTime.now().add(Duration(minutes: 5)),
  isRepeating: false,
);
```

## 4. 故障排除

### 4.1 常见问题

**问题1: 通知不显示**
- 检查设备通知权限是否开启
- 确认应用前台/后台通知设置
- 验证Android的电池优化设置

**问题2: FCM Token获取失败**
- 检查Firebase配置文件是否正确放置
- 确认网络连接正常
- 验证Google Services是否正确集成

**问题3: iOS通知权限被拒绝**
- 引导用户到系统设置中手动开启
- 检查Info.plist中的权限描述

**问题4: 定时通知不准确**
- Android: 检查电池优化白名单
- iOS: 确保后台刷新开启
- 验证时区设置是否正确

### 4.2 调试日志

启用详细日志输出:
```dart
// 在NotificationService中添加调试信息
print('🔔 通知权限状态: ${settings.authorizationStatus}');
print('🎯 FCM Token: $_fcmToken');
print('⏰ 安排提醒: $habitName 在 ${scheduledTime.hour}:${scheduledTime.minute}');
```

### 4.3 性能优化

1. **懒加载**: 仅在需要时初始化通知服务
2. **缓存管理**: 定期清理过期的通知
3. **电池优化**: 引导用户将应用加入白名单

## 5. 生产环境配置

### 5.1 发布前检查清单
- [ ] Firebase项目配置正确
- [ ] 推送证书配置（iOS）
- [ ] 权限请求文案完善
- [ ] 通知图标和样式美化
- [ ] 错误处理机制完善
- [ ] 用户隐私声明更新

### 5.2 监控和分析
- 使用Firebase Analytics跟踪通知效果
- 监控通知送达率和点击率
- 收集用户反馈优化体验

## 6. 扩展功能

### 6.1 高级提醒类型
- 地理位置提醒
- 智能学习用户习惯
- 情境感知提醒

### 6.2 个性化定制
- 自定义提醒音效
- 主题化通知样式
- 多语言支持

---

## 技术支持

如有问题，请参考:
- [Firebase文档](https://firebase.google.com/docs)
- [flutter_local_notifications文档](https://pub.dev/packages/flutter_local_notifications)
- [项目GitHub Issues](https://github.com/your-project/issues)

集成完成后，您的应用将拥有完整的习惯提醒功能！🎉 