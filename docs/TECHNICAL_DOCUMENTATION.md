# LifeHabit 技术文档

## 📋 **目录**

1. [技术架构概览](#技术架构概览)
2. [核心功能实现](#核心功能实现)
3. [数据库设计](#数据库设计)
4. [API接口设计](#api接口设计)
5. [状态管理](#状态管理)
6. [本地存储策略](#本地存储策略)
7. [网络层设计](#网络层设计)
8. [版本更新机制](#版本更新机制)
9. [性能优化](#性能优化)
10. [安全策略](#安全策略)

## 🏗️ **技术架构概览**

### **整体架构**
```
┌─────────────────────────────────────────────────────────┐
│                    Flutter App                         │
├─────────────────────────────────────────────────────────┤
│  Presentation Layer (Views & Controllers)              │
│  ├── GetX State Management                             │
│  ├── Route Management                                  │
│  └── UI Components                                     │
├─────────────────────────────────────────────────────────┤
│  Business Logic Layer                                  │
│  ├── Services (Version, Share, Cache)                 │
│  ├── Utils (Network, Storage, Toast)                  │
│  └── Models (Data Transfer Objects)                   │
├─────────────────────────────────────────────────────────┤
│  Data Access Layer                                     │
│  ├── DAOs (Data Access Objects)                       │
│  ├── Local Storage (SharedPreferences, GetStorage)    │
│  └── Network Layer (Dio HTTP Client)                  │
├─────────────────────────────────────────────────────────┤
│  External Services                                     │
│  ├── Backend API (RESTful)                            │
│  ├── Qiniu Cloud Storage                              │
│  └── Push Notification Service                        │
└─────────────────────────────────────────────────────────┘
```

### **技术栈选择**

#### **前端框架**
- **Flutter 3.3.0+**: 跨平台开发框架
  - 单一代码库支持iOS和Android
  - 高性能渲染引擎
  - 丰富的UI组件库

#### **状态管理**
- **GetX 4.7.2**: 轻量级状态管理解决方案
  - 响应式编程模式
  - 依赖注入
  - 路由管理

#### **网络请求**
- **Dio 5.8.0**: HTTP客户端
  - 拦截器支持
  - 请求/响应转换
  - 错误处理

#### **本地存储**
- **SharedPreferences**: 简单键值对存储
- **GetStorage**: 高性能本地存储
- **Flutter Cache Manager**: 图片缓存管理

## 🔧 **核心功能实现**

### **1. 习惯管理系统**

#### **数据模型设计**
```dart
class HabitModel {
  int? id;
  String name;
  String? description;
  HabitType type; // 打卡、计时、计数
  HabitFrequency frequency; // 每日、每周、自定义
  DateTime createdAt;
  DateTime? updatedAt;
  bool isActive;
  HabitSettings settings;
  List<HabitRecord> records;
}

class HabitRecord {
  int? id;
  int habitId;
  DateTime date;
  bool isCompleted;
  int? duration; // 计时型习惯的时长
  int? count; // 计数型习惯的数量
  String? note;
}
```

#### **状态管理实现**
```dart
class HabitController extends GetxController {
  final RxList<HabitModel> habits = <HabitModel>[].obs;
  final RxBool isLoading = false.obs;
  
  @override
  void onInit() {
    super.onInit();
    loadHabits();
  }
  
  Future<void> loadHabits() async {
    isLoading.value = true;
    try {
      final habitList = await HabitDao.getHabits();
      habits.assignAll(habitList);
    } catch (e) {
      showErrorToast('加载习惯失败');
    } finally {
      isLoading.value = false;
    }
  }
  
  Future<void> createHabit(HabitModel habit) async {
    try {
      final newHabit = await HabitDao.createHabit(habit);
      habits.add(newHabit);
      showSuccessToast('习惯创建成功');
    } catch (e) {
      showErrorToast('创建失败');
    }
  }
}
```

### **2. 激励系统实现**

#### **激励弹窗机制**
```dart
class MotivationService {
  static Future<void> checkAndShowMotivation() async {
    final motivationHabits = await _getMotivationHabits();
    final completedToday = await _getCompletedMotivationHabits();
    
    if (motivationHabits.isNotEmpty && 
        completedToday.length == motivationHabits.length) {
      _showMotivationDialog();
    }
  }
  
  static void _showMotivationDialog() {
    Get.dialog(
      MotivationDialog(
        onImageTap: () => _showFullScreenImage(),
        onClose: () => Get.back(),
      ),
      barrierDismissible: false,
    );
  }
}
```

#### **个性化激励图片**
```dart
class MotivationImageService {
  static Future<String> uploadMotivationImage(File imageFile) async {
    try {
      final imageKey = _generateImageKey();
      final success = await QiniuStorage().putFile(
        imageFile, 
        imageKey, 
        storageVisiblePrivate
      );
      
      if (success) {
        return imageKey;
      } else {
        throw Exception('上传失败');
      }
    } catch (e) {
      throw Exception('上传图片失败: $e');
    }
  }
  
  static String _generateImageKey() {
    final userId = LoginDao.getUserId();
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return 'motivation/${userId}_${timestamp}.jpg';
  }
}
```

### **3. 数据统计与可视化**

#### **统计数据计算**
```dart
class HabitStatisticsService {
  static HabitStatistics calculateStatistics(List<HabitRecord> records) {
    final now = DateTime.now();
    final last30Days = _getLast30DaysRecords(records, now);
    final last7Days = _getLast7DaysRecords(records, now);
    
    return HabitStatistics(
      totalDays: records.length,
      completedDays: records.where((r) => r.isCompleted).length,
      currentStreak: _calculateCurrentStreak(records),
      longestStreak: _calculateLongestStreak(records),
      completionRate: _calculateCompletionRate(records),
      weeklyTrend: _calculateWeeklyTrend(last7Days),
      monthlyTrend: _calculateMonthlyTrend(last30Days),
    );
  }
  
  static int _calculateCurrentStreak(List<HabitRecord> records) {
    if (records.isEmpty) return 0;
    
    final sortedRecords = records
        .where((r) => r.isCompleted)
        .toList()
        ..sort((a, b) => b.date.compareTo(a.date));
    
    int streak = 0;
    DateTime? lastDate;
    
    for (final record in sortedRecords) {
      if (lastDate == null) {
        streak = 1;
        lastDate = record.date;
      } else {
        final daysDiff = lastDate.difference(record.date).inDays;
        if (daysDiff == 1) {
          streak++;
          lastDate = record.date;
        } else {
          break;
        }
      }
    }
    
    return streak;
  }
}
```

#### **图表数据处理**
```dart
class ChartDataProcessor {
  static List<FlSpot> processWeeklyData(List<HabitRecord> records) {
    final weekData = <int, int>{};
    final now = DateTime.now();
    
    // 初始化一周的数据
    for (int i = 6; i >= 0; i--) {
      final date = now.subtract(Duration(days: i));
      weekData[i] = 0;
    }
    
    // 填充实际数据
    for (final record in records) {
      final daysDiff = now.difference(record.date).inDays;
      if (daysDiff >= 0 && daysDiff < 7 && record.isCompleted) {
        weekData[6 - daysDiff] = (weekData[6 - daysDiff] ?? 0) + 1;
      }
    }
    
    return weekData.entries
        .map((e) => FlSpot(e.key.toDouble(), e.value.toDouble()))
        .toList();
  }
}
```

### **4. 版本更新系统**

#### **版本检测服务**
```dart
class VersionUpdateService {
  static const String _lastCheckKey = 'last_version_check';
  static const Duration _checkInterval = Duration(hours: 12);
  
  static Future<void> checkForUpdate({
    bool forceCheck = false,
    bool showNoUpdateToast = false,
  }) async {
    try {
      if (!forceCheck && !await _shouldCheck()) return;
      
      final packageInfo = await PackageInfo.fromPlatform();
      final currentVersion = packageInfo.version;
      final platform = Platform.isIOS ? 'ios' : 'android';
      
      final versionData = await SettingDao.checkVersion(
        currentVersion, 
        platform
      );
      final versionInfo = VersionData.fromJson(versionData);
      
      await _updateLastCheckTime();
      
      if (versionInfo.hasUpdate) {
        if (!versionInfo.forceUpdate && 
            await _isVersionSkipped(versionInfo.latestVersion)) {
          return;
        }
        _showUpdateDialog(versionInfo);
      } else if (showNoUpdateToast) {
        showSuccessToast("当前已是最新版本");
      }
    } catch (e) {
      if (showNoUpdateToast) {
        showErrorToast("检查更新失败，请稍后重试");
      }
    }
  }
}
```

## 🗄️ **数据库设计**

### **核心表结构**

#### **用户表 (users)**
```sql
CREATE TABLE users (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  username VARCHAR(50) NOT NULL,
  email VARCHAR(100) UNIQUE,
  phone VARCHAR(20) UNIQUE,
  avatar_url VARCHAR(500),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  is_active BOOLEAN DEFAULT TRUE
);
```

#### **习惯表 (habits)**
```sql
CREATE TABLE habits (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  user_id BIGINT NOT NULL,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  type ENUM('check', 'timer', 'counter') NOT NULL,
  frequency ENUM('daily', 'weekly', 'custom') DEFAULT 'daily',
  target_value INT DEFAULT 1,
  unit VARCHAR(20),
  color VARCHAR(7) DEFAULT '#10B981',
  icon VARCHAR(50),
  reminder_time TIME,
  is_reminder_enabled BOOLEAN DEFAULT FALSE,
  is_motivation_enabled BOOLEAN DEFAULT FALSE,
  motivation_image_key VARCHAR(200),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  is_active BOOLEAN DEFAULT TRUE,
  FOREIGN KEY (user_id) REFERENCES users(id)
);
```

#### **习惯记录表 (habit_records)**
```sql
CREATE TABLE habit_records (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  habit_id BIGINT NOT NULL,
  user_id BIGINT NOT NULL,
  record_date DATE NOT NULL,
  is_completed BOOLEAN DEFAULT FALSE,
  actual_value INT DEFAULT 0,
  duration_seconds INT DEFAULT 0,
  note TEXT,
  completed_at TIMESTAMP NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY unique_habit_date (habit_id, record_date),
  FOREIGN KEY (habit_id) REFERENCES habits(id),
  FOREIGN KEY (user_id) REFERENCES users(id)
);
```

#### **用户统计表 (user_statistics)**
```sql
CREATE TABLE user_statistics (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  user_id BIGINT NOT NULL,
  total_habits INT DEFAULT 0,
  active_habits INT DEFAULT 0,
  completed_habits INT DEFAULT 0,
  total_check_ins INT DEFAULT 0,
  current_streak INT DEFAULT 0,
  longest_streak INT DEFAULT 0,
  last_calculated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id)
);
```

### **索引优化**
```sql
-- 习惯查询优化
CREATE INDEX idx_habits_user_active ON habits(user_id, is_active);
CREATE INDEX idx_habits_reminder ON habits(is_reminder_enabled, reminder_time);

-- 记录查询优化
CREATE INDEX idx_records_user_date ON habit_records(user_id, record_date);
CREATE INDEX idx_records_habit_date ON habit_records(habit_id, record_date);
CREATE INDEX idx_records_completed ON habit_records(is_completed, record_date);
```

## 🌐 **API接口设计**

### **RESTful API规范**

#### **基础响应格式**
```json
{
  "code": 0,
  "msg": "success",
  "data": {},
  "timestamp": 1703123456789
}
```

#### **习惯管理接口**

**获取用户习惯列表**
```http
GET /api/v1/habits
Authorization: Bearer {token}

Response:
{
  "code": 0,
  "msg": "success",
  "data": {
    "habits": [
      {
        "id": 1,
        "name": "早起",
        "type": "check",
        "frequency": "daily",
        "isActive": true,
        "stats": {
          "currentStreak": 5,
          "completionRate": 0.85
        }
      }
    ]
  }
}
```

**创建习惯**
```http
POST /api/v1/habits
Authorization: Bearer {token}
Content-Type: application/json

{
  "name": "晨跑",
  "description": "每天早上跑步30分钟",
  "type": "timer",
  "frequency": "daily",
  "targetValue": 30,
  "unit": "分钟",
  "reminderTime": "07:00",
  "isReminderEnabled": true
}
```

**更新习惯记录**
```http
POST /api/v1/habits/{habitId}/records
Authorization: Bearer {token}
Content-Type: application/json

{
  "date": "2024-12-01",
  "isCompleted": true,
  "actualValue": 30,
  "durationSeconds": 1800,
  "note": "今天跑得很轻松"
}
```

#### **版本检测接口**
```http
GET /api/v1/version/check?current_version=1.1.0&platform=android
Authorization: Bearer {token}

Response:
{
  "code": 0,
  "msg": "success",
  "data": {
    "hasUpdate": true,
    "forceUpdate": false,
    "latestVersion": "1.2.0",
    "updateTitle": "发现新版本",
    "updateContent": "• 全新UI设计\n• 性能优化\n• 修复已知问题",
    "downloadUrl": "https://example.com/app.apk",
    "iosAppStoreUrl": "https://apps.apple.com/app/id6473467524",
    "androidDirectUrl": "https://example.com/android.apk",
    "fileSize": 25600000
  }
}
```

## 🔄 **状态管理**

### **GetX状态管理架构**

#### **控制器层次结构**
```
AppController (全局状态)
├── UserController (用户状态)
├── HabitController (习惯管理)
├── StatisticsController (统计数据)
├── MotivationController (激励系统)
└── SettingsController (应用设置)
```

#### **响应式状态示例**
```dart
class HabitController extends GetxController {
  // 响应式变量
  final RxList<HabitModel> habits = <HabitModel>[].obs;
  final RxBool isLoading = false.obs;
  final RxString selectedDate = ''.obs;
  
  // 计算属性
  List<HabitModel> get todayHabits => habits
      .where((habit) => habit.isActiveToday)
      .toList();
  
  int get completedTodayCount => todayHabits
      .where((habit) => habit.isCompletedToday)
      .length;
  
  double get todayCompletionRate => todayHabits.isEmpty 
      ? 0.0 
      : completedTodayCount / todayHabits.length;
  
  // 状态更新方法
  void updateHabitCompletion(int habitId, bool isCompleted) {
    final habitIndex = habits.indexWhere((h) => h.id == habitId);
    if (habitIndex != -1) {
      habits[habitIndex].updateTodayRecord(isCompleted);
      habits.refresh(); // 触发UI更新
    }
  }
}
```

#### **依赖注入管理**
```dart
class DependencyInjection {
  static void init() {
    // 核心服务
    Get.put<NetworkService>(NetworkService(), permanent: true);
    Get.put<StorageService>(StorageService(), permanent: true);
    Get.put<CacheService>(CacheService(), permanent: true);
    
    // 业务控制器
    Get.lazyPut<UserController>(() => UserController());
    Get.lazyPut<HabitController>(() => HabitController());
    Get.lazyPut<StatisticsController>(() => StatisticsController());
  }
}
```

## 💾 **本地存储策略**

### **存储层次设计**

#### **SharedPreferences (轻量级配置)**
```dart
class PreferenceKeys {
  static const String userId = 'user_id';
  static const String userToken = 'user_token';
  static const String lastSyncTime = 'last_sync_time';
  static const String appLanguage = 'app_language';
  static const String themeMode = 'theme_mode';
  static const String notificationEnabled = 'notification_enabled';
}

class PreferenceService {
  static SharedPreferences? _prefs;
  
  static Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
  }
  
  static Future<void> setUserId(int userId) async {
    await _prefs?.setInt(PreferenceKeys.userId, userId);
  }
  
  static int? getUserId() {
    return _prefs?.getInt(PreferenceKeys.userId);
  }
}
```

#### **GetStorage (高性能存储)**
```dart
class CacheService extends GetxService {
  late GetStorage _storage;
  
  @override
  Future<void> onInit() async {
    super.onInit();
    await GetStorage.init('habit_cache');
    _storage = GetStorage('habit_cache');
  }
  
  // 习惯数据缓存
  void cacheHabits(List<HabitModel> habits) {
    final habitsJson = habits.map((h) => h.toJson()).toList();
    _storage.write('cached_habits', habitsJson);
    _storage.write('habits_cache_time', DateTime.now().toIso8601String());
  }
  
  List<HabitModel>? getCachedHabits() {
    final cacheTime = _storage.read('habits_cache_time');
    if (cacheTime != null) {
      final cached = DateTime.parse(cacheTime);
      final now = DateTime.now();
      
      // 缓存有效期1小时
      if (now.difference(cached).inHours < 1) {
        final habitsJson = _storage.read('cached_habits') as List?;
        return habitsJson?.map((json) => HabitModel.fromJson(json)).toList();
      }
    }
    return null;
  }
}
```

### **数据同步策略**

#### **离线优先设计**
```dart
class DataSyncService {
  static Future<void> syncHabitRecord(HabitRecord record) async {
    try {
      // 先保存到本地
      await LocalDatabase.saveRecord(record);
      
      // 尝试同步到服务器
      await _syncToServer(record);
    } catch (e) {
      // 网络失败时标记为待同步
      await LocalDatabase.markForSync(record);
    }
  }
  
  static Future<void> syncPendingData() async {
    final pendingRecords = await LocalDatabase.getPendingRecords();
    
    for (final record in pendingRecords) {
      try {
        await _syncToServer(record);
        await LocalDatabase.markSynced(record);
      } catch (e) {
        // 同步失败，保持待同步状态
        print('同步失败: ${record.id}');
      }
    }
  }
}
```

## 🌐 **网络层设计**

### **HTTP客户端配置**
```dart
class HiNet {
  static HiNet? _instance;
  late Dio _dio;
  
  static HiNet getInstance() {
    _instance ??= HiNet._internal();
    return _instance!;
  }
  
  HiNet._internal() {
    _dio = Dio(BaseOptions(
      baseUrl: HiConstants.baseUrl,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ));
    
    _setupInterceptors();
  }
  
  void _setupInterceptors() {
    // 请求拦截器
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) {
        // 添加认证token
        final token = LoginDao.getToken();
        if (token != null) {
          options.headers['Authorization'] = 'Bearer $token';
        }
        
        // 添加设备信息
        options.headers['User-Agent'] = _getUserAgent();
        
        handler.next(options);
      },
      
      onResponse: (response, handler) {
        // 统一响应处理
        if (response.data['code'] != 0) {
          throw HiNetError(
            response.data['code'],
            response.data['msg'] ?? '请求失败',
          );
        }
        handler.next(response);
      },
      
      onError: (error, handler) {
        // 统一错误处理
        if (error.response?.statusCode == 401) {
          // token过期，跳转登录
          _handleTokenExpired();
        }
        handler.next(error);
      },
    ));
    
    // 日志拦截器
    if (kDebugMode) {
      _dio.interceptors.add(LogInterceptor(
        requestBody: true,
        responseBody: true,
      ));
    }
  }
}
```

### **请求封装**
```dart
abstract class BaseRequest {
  String path();
  HttpMethod httpMethod();
  bool needLogin();
  
  Map<String, dynamic> params = {};
  
  void add(String key, Object value) {
    params[key] = value;
  }
}

class HabitListRequest extends BaseRequest {
  @override
  String path() => '/api/v1/habits';
  
  @override
  HttpMethod httpMethod() => HttpMethod.GET;
  
  @override
  bool needLogin() => true;
}
```

### **错误处理机制**
```dart
class HiNetError implements Exception {
  final int code;
  final String message;
  
  HiNetError(this.code, this.message);
  
  @override
  String toString() => 'HiNetError: $code - $message';
}

Future<T> commonPerformRequest<T>({
  required Future<T> Function() requestFunction,
}) async {
  try {
    return await requestFunction();
  } on HiNetError catch (e) {
    _handleBusinessError(e);
    rethrow;
  } on DioException catch (e) {
    _handleNetworkError(e);
    rethrow;
  } catch (e) {
    _handleUnknownError(e);
    rethrow;
  }
}
```

## 🚀 **性能优化**

### **图片加载优化**
```dart
class CachedImageWidget extends StatelessWidget {
  final String imageUrl;
  final double? width;
  final double? height;
  final BoxFit fit;
  
  const CachedImageWidget({
    Key? key,
    required this.imageUrl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return CachedNetworkImage(
      imageUrl: imageUrl,
      width: width,
      height: height,
      fit: fit,
      placeholder: (context, url) => _buildPlaceholder(),
      errorWidget: (context, url, error) => _buildErrorWidget(),
      cacheManager: CustomCacheManager.instance,
      memCacheWidth: width?.toInt(),
      memCacheHeight: height?.toInt(),
    );
  }
}

class CustomCacheManager {
  static CacheManager? _instance;
  
  static CacheManager get instance {
    _instance ??= CacheManager(
      Config(
        'habit_image_cache',
        stalePeriod: const Duration(days: 7),
        maxNrOfCacheObjects: 200,
        repo: JsonCacheInfoRepository(databaseName: 'habit_cache.db'),
      ),
    );
    return _instance!;
  }
}
```

### **列表性能优化**
```dart
class OptimizedHabitList extends StatelessWidget {
  final List<HabitModel> habits;
  
  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: habits.length,
      itemExtent: 80.0, // 固定item高度，提升性能
      cacheExtent: 500.0, // 预缓存范围
      itemBuilder: (context, index) {
        return HabitListItem(
          key: ValueKey(habits[index].id), // 使用稳定的key
          habit: habits[index],
        );
      },
    );
  }
}

class HabitListItem extends StatelessWidget {
  final HabitModel habit;
  
  const HabitListItem({
    Key? key,
    required this.habit,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    // 使用const构造函数和缓存
    return RepaintBoundary(
      child: Container(
        height: 80,
        child: _buildContent(),
      ),
    );
  }
}
```

### **内存管理**
```dart
class MemoryManager {
  static void clearImageCache() {
    PaintingBinding.instance.imageCache.clear();
    PaintingBinding.instance.imageCache.clearLiveImages();
  }
  
  static void optimizeImageCache() {
    PaintingBinding.instance.imageCache.maximumSize = 100;
    PaintingBinding.instance.imageCache.maximumSizeBytes = 50 << 20; // 50MB
  }
  
  static void disposeUnusedResources() {
    // 清理未使用的控制器
    Get.delete<UnusedController>();
    
    // 清理过期缓存
    CustomCacheManager.instance.emptyCache();
  }
}
```

## 🔒 **安全策略**

### **数据加密**
```dart
class EncryptionService {
  static const String _key = 'your-encryption-key';
  
  static String encrypt(String plainText) {
    // 使用AES加密敏感数据
    final key = encrypt.Key.fromBase64(_key);
    final iv = encrypt.IV.fromSecureRandom(16);
    final encrypter = encrypt.Encrypter(encrypt.AES(key));
    
    final encrypted = encrypter.encrypt(plainText, iv: iv);
    return '${iv.base64}:${encrypted.base64}';
  }
  
  static String decrypt(String encryptedText) {
    final parts = encryptedText.split(':');
    final iv = encrypt.IV.fromBase64(parts[0]);
    final encrypted = encrypt.Encrypted.fromBase64(parts[1]);
    
    final key = encrypt.Key.fromBase64(_key);
    final encrypter = encrypt.Encrypter(encrypt.AES(key));
    
    return encrypter.decrypt(encrypted, iv: iv);
  }
}
```

### **网络安全**
```dart
class SecurityInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    // 添加请求签名
    final timestamp = DateTime.now().millisecondsSinceEpoch.toString();
    final signature = _generateSignature(options.data, timestamp);
    
    options.headers['X-Timestamp'] = timestamp;
    options.headers['X-Signature'] = signature;
    
    // 确保HTTPS
    if (!options.uri.scheme.startsWith('https')) {
      throw DioException(
        requestOptions: options,
        error: 'Only HTTPS requests are allowed',
      );
    }
    
    handler.next(options);
  }
  
  String _generateSignature(dynamic data, String timestamp) {
    final content = '${jsonEncode(data)}$timestamp';
    final bytes = utf8.encode(content);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }
}
```

### **本地数据保护**
```dart
class SecureStorage {
  static const FlutterSecureStorage _storage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      accessibility: IOSAccessibility.first_unlock_this_device,
    ),
  );
  
  static Future<void> storeToken(String token) async {
    await _storage.write(key: 'auth_token', value: token);
  }
  
  static Future<String?> getToken() async {
    return await _storage.read(key: 'auth_token');
  }
  
  static Future<void> clearAll() async {
    await _storage.deleteAll();
  }
}
```

## 📊 **监控与分析**

### **性能监控**
```dart
class PerformanceMonitor {
  static void trackPageLoad(String pageName) {
    final stopwatch = Stopwatch()..start();
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      stopwatch.stop();
      _reportPageLoadTime(pageName, stopwatch.elapsedMilliseconds);
    });
  }
  
  static void trackApiCall(String apiName, Duration duration, bool success) {
    _reportApiMetrics(apiName, duration.inMilliseconds, success);
  }
  
  static void _reportPageLoadTime(String pageName, int milliseconds) {
    // 发送到分析服务
    print('Page $pageName loaded in ${milliseconds}ms');
  }
}
```

### **错误追踪**
```dart
class ErrorTracker {
  static void reportError(dynamic error, StackTrace stackTrace) {
    final errorInfo = {
      'error': error.toString(),
      'stackTrace': stackTrace.toString(),
      'timestamp': DateTime.now().toIso8601String(),
      'userId': LoginDao.getUserId(),
      'appVersion': '1.1.0',
    };
    
    // 发送到错误追踪服务
    _sendErrorReport(errorInfo);
  }
  
  static void _sendErrorReport(Map<String, dynamic> errorInfo) {
    // 实现错误报告逻辑
  }
}
```

---

*本技术文档最后更新时间：2024年12月*
*版本：v1.1.0* 