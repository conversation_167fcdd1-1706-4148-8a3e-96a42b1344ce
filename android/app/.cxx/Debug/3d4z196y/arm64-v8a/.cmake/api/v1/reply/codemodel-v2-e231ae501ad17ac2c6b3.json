{"configurations": [{"directories": [{"build": ".", "minimumCMakeVersion": {"string": "3.6.0"}, "projectIndex": 0, "source": "."}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "Project"}], "targets": []}], "kind": "codemodel", "paths": {"build": "/Users/<USER>/my_pro/life_habit_app/android/app/.cxx/Debug/3d4z196y/arm64-v8a", "source": "/Users/<USER>/flutter/packages/flutter_tools/gradle/src/main/groovy"}, "version": {"major": 2, "minor": 1}}