{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake", "cpack": "/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cpack", "ctest": "/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ctest", "root": "/Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": false, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-6de4313d92e8bfa05232.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-8dc830eab6a2a572af60.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-390085027f1e6038acb7.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-8dc830eab6a2a572af60.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-390085027f1e6038acb7.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-6de4313d92e8bfa05232.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}