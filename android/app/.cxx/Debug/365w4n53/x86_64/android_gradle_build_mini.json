{"buildFiles": ["/Users/<USER>/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/my_pro/life_habit_app/android/app/.cxx/Debug/365w4n53/x86_64", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/my_pro/life_habit_app/android/app/.cxx/Debug/365w4n53/x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}